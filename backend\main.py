"""
FastAPI 应用主文件
定义所有 API 路由和业务逻辑
"""

import logging
import sqlite3
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Dict, Any

# 导入自定义模块
from database import init_db, add_account, get_account_by_email, get_all_accounts, delete_account, update_account_status
from mail_fetcher import get_emails_for_account
from models import (
    AccountCreate, AccountResponse, AccountDetail, 
    MailResponse, MailMessage, ErrorResponse, SuccessResponse
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# 创建 FastAPI 应用实例
app = FastAPI(
    title="多账户邮件聚合 API 服务",
    description="一个用于管理多个微软邮箱账户并获取邮件的 RESTful API 服务",
    version="1.0.0"
)

# 启动时初始化数据库
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("正在初始化数据库...")
    try:
        init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径 - API 服务状态"""
    return {
        "message": "多账户邮件聚合 API 服务",
        "status": "运行中",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.post("/api/accounts", response_model=AccountResponse, status_code=201)
async def create_account(account_data: AccountCreate):
    """
    添加新的邮箱账户
    
    Args:
        account_data: 账户创建数据
        
    Returns:
        AccountResponse: 创建的账户信息（不包含敏感数据）
        
    Raises:
        HTTPException: 如果邮箱地址已存在或其他错误
    """
    try:
        # 检查邮箱是否已存在
        existing_account = get_account_by_email(account_data.email_address)
        if existing_account:
            raise HTTPException(
                status_code=409,
                detail=f"邮箱地址 {account_data.email_address} 已存在"
            )
        
        # 添加新账户
        new_account = add_account(account_data)
        
        # 返回响应（不包含 refresh_token）
        return AccountResponse(
            id=new_account.id,
            email_address=new_account.email_address,
            client_id=new_account.client_id,
            status=new_account.status,
            last_updated=new_account.last_updated
        )
        
    except sqlite3.IntegrityError:
        raise HTTPException(
            status_code=409,
            detail=f"邮箱地址 {account_data.email_address} 已存在"
        )
    except Exception as e:
        logger.error(f"创建账户时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建账户失败: {str(e)}"
        )


@app.get("/api/accounts", response_model=List[AccountResponse])
async def get_accounts():
    """
    获取所有账户信息
    
    Returns:
        List[AccountResponse]: 所有账户的列表（不包含敏感数据）
    """
    try:
        accounts = get_all_accounts()
        
        # 转换为响应模型
        return [
            AccountResponse(
                id=account['id'],
                email_address=account['email_address'],
                client_id=account['client_id'],
                status=account['status'],
                last_updated=account['last_updated']
            )
            for account in accounts
        ]
        
    except Exception as e:
        logger.error(f"获取账户列表时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取账户列表失败: {str(e)}"
        )


@app.delete("/api/accounts/{email_address}", response_model=SuccessResponse)
async def delete_account_by_email(email_address: str):
    """
    删除指定邮箱的账户
    
    Args:
        email_address: 要删除的邮箱地址
        
    Returns:
        SuccessResponse: 删除成功的响应
        
    Raises:
        HTTPException: 如果账户不存在
    """
    try:
        # 检查账户是否存在
        existing_account = get_account_by_email(email_address)
        if not existing_account:
            raise HTTPException(
                status_code=404,
                detail=f"邮箱地址 {email_address} 不存在"
            )
        
        # 删除账户
        success = delete_account(email_address)
        
        if success:
            return SuccessResponse(
                message=f"成功删除账户: {email_address}",
                data={"email_address": email_address}
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="删除账户失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除账户时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除账户失败: {str(e)}"
        )


@app.get("/api/accounts/{email_address}", response_model=AccountResponse)
async def get_account_info(email_address: str):
    """
    获取指定邮箱的账户信息
    
    Args:
        email_address: 邮箱地址
        
    Returns:
        AccountResponse: 账户信息（不包含敏感数据）
        
    Raises:
        HTTPException: 如果账户不存在
    """
    try:
        account = get_account_by_email(email_address)
        
        if not account:
            raise HTTPException(
                status_code=404,
                detail=f"邮箱地址 {email_address} 不存在"
            )
        
        return AccountResponse(
            id=account.id,
            email_address=account.email_address,
            client_id=account.client_id,
            status=account.status,
            last_updated=account.last_updated
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取账户信息时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取账户信息失败: {str(e)}"
        )


# 异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP_ERROR",
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "服务器内部错误",
            "status_code": 500
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    logger.info("启动 FastAPI 应用...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
