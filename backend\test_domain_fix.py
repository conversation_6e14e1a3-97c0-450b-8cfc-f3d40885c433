"""
测试域名修复功能
验证不同域名使用正确的OAuth2端点
"""

from mail_fetcher import MailFetcher
import logging

# 配置日志以查看详细信息
logging.basicConfig(level=logging.INFO)

def test_domain_endpoint_selection():
    """测试域名端点选择功能"""
    print("🔧 测试域名端点选择功能")
    print("=" * 50)
    
    fetcher = MailFetcher()
    
    # 测试不同域名的端点选择
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    expected_endpoints = {
        "<EMAIL>": "https://login.microsoftonline.com/common/oauth2/v2.0/token",
        "<EMAIL>": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token",
        "<EMAIL>": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token",
        "<EMAIL>": "https://login.microsoftonline.com/common/oauth2/v2.0/token"  # 默认端点
    }
    
    print("📋 测试端点选择:")
    for email in test_emails:
        actual_endpoint = fetcher.get_token_url_for_domain(email)
        expected_endpoint = expected_endpoints[email]
        
        status = "✅" if actual_endpoint == expected_endpoint else "❌"
        print(f"   {status} {email}")
        print(f"      期望: {expected_endpoint}")
        print(f"      实际: {actual_endpoint}")
        print()

def test_domain_config():
    """测试域名配置"""
    print("📊 域名配置信息:")
    print("=" * 50)
    
    fetcher = MailFetcher()
    
    for domain, config in fetcher.domain_config.items():
        print(f"   🌐 {domain}")
        print(f"      端点: {config['token_url']}")
        print(f"      说明: {config['description']}")
        print()
    
    print(f"   🔧 默认端点: {fetcher.default_token_url}")

def test_backward_compatibility():
    """测试向后兼容性"""
    print("🔄 测试向后兼容性:")
    print("=" * 50)
    
    fetcher = MailFetcher()
    
    # 测试不传入email_address参数的情况
    print("   📝 测试不传入email_address参数...")
    
    # 这应该使用默认端点并记录警告
    try:
        # 注意：这里使用虚假的凭证，预期会失败，但我们只是测试端点选择
        success, result = fetcher.get_access_token("fake_client_id", "fake_refresh_token")
        print(f"   ✅ 方法调用成功（预期Token失败）")
        print(f"   📄 结果: {result}")
    except Exception as e:
        print(f"   ❌ 方法调用异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试域名修复功能")
    print("=" * 60)
    
    test_domain_config()
    print()
    
    test_domain_endpoint_selection()
    print()
    
    test_backward_compatibility()
    
    print("=" * 60)
    print("✅ 域名修复功能测试完成")
    print()
    print("📋 修复说明:")
    print("   - @outlook.com 账户使用 common 端点")
    print("   - @hotmail.com 账户使用 consumers 端点") 
    print("   - @live.com 账户使用 consumers 端点")
    print("   - 未知域名使用默认 common 端点")
    print("   - 保持向后兼容性")
