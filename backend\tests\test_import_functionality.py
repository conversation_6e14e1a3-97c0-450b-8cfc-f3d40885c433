"""
邮箱账户导入功能测试脚本
验证前端导入功能的完整流程
"""

import requests
import json
import time
import os

# API 基础 URL
API_BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

def test_backend_import_api():
    """测试后端导入 API"""
    print("🔧 测试后端导入 API...")
    
    # 测试单个账户添加
    test_account = {
        "email_address": "<EMAIL>",
        "client_id": "test1234-5678-90ab-cdef-123456789abc",
        "refresh_token": "0.AQsAtest123def456ghi789jkl012mno345pqr678stu901vwx234yzA.AgABAAAAAAD--DLA3VO7QrddgJg7WevrAgDs_wQA9P8test123456789"
    }
    
    try:
        # 先删除可能存在的测试账户
        try:
            accounts_response = requests.get(f"{API_BASE_URL}/api/accounts")
            if accounts_response.status_code == 200:
                accounts = accounts_response.json()
                for account in accounts:
                    if account['email_address'] == test_account['email_address']:
                        print(f"   删除已存在的测试账户: {account['email_address']}")
                        # 这里假设有删除接口，如果没有可以跳过
        except:
            pass
        
        # 测试添加账户
        response = requests.post(
            f"{API_BASE_URL}/api/accounts",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            print("   ✅ 后端添加账户接口正常")
            result = response.json()
            print(f"   📧 添加账户: {result['email_address']}")
            print(f"   🆔 账户 ID: {result['id']}")
            print(f"   📊 状态: {result['status']}")
            return True
        elif response.status_code == 409:
            print("   ✅ 后端接口正常（账户已存在）")
            return True
        else:
            print(f"   ❌ 后端接口异常: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 后端接口测试失败: {e}")
        return False

def test_csv_file_format():
    """测试 CSV 文件格式"""
    print("\n📁 测试 CSV 文件格式...")
    
    csv_file = "frontend/test_accounts_import.csv"
    
    if not os.path.exists(csv_file):
        print(f"   ❌ CSV 测试文件不存在: {csv_file}")
        return False
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"   ✅ CSV 文件读取成功，共 {len(lines)} 行")
        
        # 检查标题行
        if lines[0].strip().lower().startswith('email_address'):
            print("   ✅ CSV 标题行格式正确")
        else:
            print("   ⚠️ CSV 标题行格式可能有问题")
        
        # 检查数据行
        data_lines = len(lines) - 1
        print(f"   📊 数据行数: {data_lines}")
        
        # 验证第一行数据
        if len(lines) > 1:
            first_data = lines[1].strip().split(',')
            if len(first_data) >= 3:
                print(f"   ✅ 数据格式正确: {first_data[0]}")
            else:
                print("   ❌ 数据格式错误")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ CSV 文件测试失败: {e}")
        return False

def test_json_file_format():
    """测试 JSON 文件格式"""
    print("\n📄 测试 JSON 文件格式...")
    
    json_file = "frontend/test_accounts_import.json"
    
    if not os.path.exists(json_file):
        print(f"   ❌ JSON 测试文件不存在: {json_file}")
        return False
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"   ✅ JSON 文件解析成功")
        
        if isinstance(data, list):
            print(f"   ✅ JSON 格式正确（数组），共 {len(data)} 个账户")
            
            # 检查第一个账户的字段
            if len(data) > 0:
                first_account = data[0]
                required_fields = ['email_address', 'client_id', 'refresh_token']
                
                for field in required_fields:
                    if field in first_account:
                        print(f"   ✅ 字段 '{field}' 存在")
                    else:
                        print(f"   ❌ 缺少字段 '{field}'")
                        return False
                
                print(f"   📧 示例账户: {first_account['email_address']}")
            
            return True
        else:
            print("   ❌ JSON 格式错误（不是数组）")
            return False
            
    except Exception as e:
        print(f"   ❌ JSON 文件测试失败: {e}")
        return False

def test_frontend_accessibility():
    """测试前端可访问性"""
    print("\n🌐 测试前端可访问性...")
    
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        
        if response.status_code == 200:
            print("   ✅ 前端服务正常访问")
            
            # 检查是否包含导入按钮
            if 'importAccountsBtn' in response.text:
                print("   ✅ 导入按钮元素存在")
            else:
                print("   ⚠️ 导入按钮元素可能不存在")
            
            # 检查是否包含模态框
            if 'importModal' in response.text:
                print("   ✅ 导入模态框元素存在")
            else:
                print("   ⚠️ 导入模态框元素可能不存在")
            
            return True
        else:
            print(f"   ❌ 前端服务异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端访问失败: {e}")
        return False

def simulate_import_workflow():
    """模拟导入工作流程"""
    print("\n🔄 模拟导入工作流程...")
    
    # 模拟解析 CSV 数据
    print("   1. 模拟文件解析...")
    csv_data = [
        {
            "email_address": "<EMAIL>",
            "client_id": "workflow1-2345-6789-abcd-ef1234567890",
            "refresh_token": "0.AQsAworkflow123def456ghi789jkl012mno345pqr678stu901vwx234yzA.AgABAAAAAAD--DLA3VO7QrddgJg7WevrAgDs_wQA9P8workflow123456789"
        },
        {
            "email_address": "<EMAIL>",
            "client_id": "workflow2-3456-789a-bcde-f12345678901",
            "refresh_token": "0.AQsAworkflow456ghi789jkl012mno345pqr678stu901vwx234yzA.AgABAAAAAAD--DLA3VO7QrddgJg7WevrAgDs_wQA9P9workflow987654321"
        }
    ]
    print(f"   ✅ 解析到 {len(csv_data)} 个账户")
    
    # 模拟数据验证
    print("   2. 模拟数据验证...")
    valid_accounts = []
    invalid_accounts = []
    
    for account in csv_data:
        # 简单验证
        if ('@' in account['email_address'] and 
            len(account['client_id']) > 10 and 
            len(account['refresh_token']) > 20):
            valid_accounts.append(account)
        else:
            invalid_accounts.append(account)
    
    print(f"   ✅ 有效账户: {len(valid_accounts)}")
    print(f"   ❌ 无效账户: {len(invalid_accounts)}")
    
    # 模拟导入过程
    if valid_accounts:
        print("   3. 模拟导入过程...")
        success_count = 0
        failure_count = 0
        
        for i, account in enumerate(valid_accounts):
            print(f"   导入进度: {i+1}/{len(valid_accounts)} - {account['email_address']}")
            
            try:
                # 实际调用后端 API
                response = requests.post(
                    f"{API_BASE_URL}/api/accounts",
                    json=account,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code in [201, 409]:  # 201=创建成功, 409=已存在
                    success_count += 1
                    print(f"   ✅ 导入成功: {account['email_address']}")
                else:
                    failure_count += 1
                    print(f"   ❌ 导入失败: {account['email_address']} - {response.status_code}")
                
                # 添加延迟模拟真实导入
                time.sleep(0.5)
                
            except Exception as e:
                failure_count += 1
                print(f"   ❌ 导入异常: {account['email_address']} - {e}")
        
        print(f"   📊 导入完成: 成功 {success_count}, 失败 {failure_count}")
        return success_count > 0
    
    return False

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告...")
    
    report = {
        "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
        "测试项目": [
            "后端导入 API",
            "CSV 文件格式",
            "JSON 文件格式", 
            "前端可访问性",
            "导入工作流程"
        ],
        "测试文件": [
            "frontend/test_accounts_import.csv",
            "frontend/test_accounts_import.json"
        ],
        "功能特性": [
            "文件上传导入",
            "手动输入导入",
            "剪贴板导入",
            "数据验证",
            "导入预览",
            "进度显示",
            "错误处理"
        ]
    }
    
    print("   ✅ 测试报告生成完成")
    print(f"   📅 测试时间: {report['测试时间']}")
    print(f"   🧪 测试项目: {len(report['测试项目'])} 项")
    print(f"   📁 测试文件: {len(report['测试文件'])} 个")
    print(f"   🎯 功能特性: {len(report['功能特性'])} 个")
    
    return report

def main():
    """主测试函数"""
    print("🚀 邮箱账户导入功能测试")
    print("=" * 60)
    
    # 执行各项测试
    tests = [
        ("后端导入 API", test_backend_import_api),
        ("CSV 文件格式", test_csv_file_format),
        ("JSON 文件格式", test_json_file_format),
        ("前端可访问性", test_frontend_accessibility),
        ("导入工作流程", simulate_import_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    report = generate_test_report()
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！导入功能可以正常使用")
        print("\n📖 使用指南:")
        print("   1. 访问前端: http://localhost:3000")
        print("   2. 点击左侧 '📥 导入邮箱' 按钮")
        print("   3. 选择导入方式：文件上传、手动输入或剪贴板")
        print("   4. 使用测试文件: test_accounts_import.csv 或 test_accounts_import.json")
        print("   5. 查看导入预览和验证结果")
        print("   6. 点击 '开始导入' 执行批量导入")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        print("\n🔧 故障排除:")
        print("   1. 确保后端服务运行在 http://localhost:8000")
        print("   2. 确保前端服务运行在 http://localhost:3000")
        print("   3. 检查测试文件是否存在且格式正确")
        print("   4. 查看控制台错误信息")

if __name__ == "__main__":
    main()
