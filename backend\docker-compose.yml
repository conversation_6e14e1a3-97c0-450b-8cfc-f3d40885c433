version: '3.8'

services:
  mail-aggregator:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - DATABASE_PATH=/app/data/accounts.db
      - LOG_FILE=/app/logs/app.log
      - FRONTEND_URL=https://your-app.netlify.app
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
