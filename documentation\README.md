# 多账户邮件聚合 API 服务

一个基于 Flask 的 RESTful API 服务，用于管理多个微软邮箱账户并获取邮件。

## 🚀 功能特性

- ✅ **多账户管理**: 支持添加、查询、删除多个邮箱账户
- ✅ **邮件获取**: 通过 API 获取指定账户的邮件
- ✅ **多文件夹支持**: 支持收件箱、垃圾邮件、已发送等文件夹
- ✅ **自动 Token 管理**: 自动使用 refresh_token 获取 access_token
- ✅ **状态监控**: 实时监控账户状态和连接状态
- ✅ **错误处理**: 完善的错误处理和状态码返回
- ✅ **安全性**: 响应中不包含敏感的 refresh_token 信息

## 📁 项目结构

```
/batch-mail-api
├── flask_main.py          # Flask API 服务器主文件
├── database.py            # 数据库操作模块
├── mail_fetcher.py        # 邮件获取核心逻辑
├── models.py              # Pydantic 数据模型
├── requirements.txt       # 项目依赖
├── accounts.db            # SQLite 数据库文件
├── API_DOCUMENTATION.md   # 详细 API 文档
├── README.md              # 项目说明文档
│
├── # 测试脚本
├── test_api.py            # API 接口测试
├── test_mail_api.py       # 邮件获取测试
├── test_database.py       # 数据库功能测试
├── add_real_accounts.py   # 批量添加账户脚本
│
└── # 备用文件
    ├── main.py            # FastAPI 版本 (兼容性问题)
    └── simple_main.py     # 简化版 FastAPI
```

## 🛠️ 技术栈

- **Web 框架**: Flask 3.1.1
- **数据库**: SQLite (内置)
- **数据验证**: Pydantic
- **HTTP 客户端**: requests
- **邮件协议**: imaplib (内置)
- **认证**: Microsoft OAuth2 + IMAP XOAUTH2

## 📦 安装和运行

### 1. 安装依赖

```bash
pip install flask requests
```

### 2. 启动服务

```bash
python flask_main.py
```

服务将在 `http://localhost:8000` 启动

### 3. 验证服务

```bash
curl http://localhost:8000/
```

## 🔧 API 使用示例

### 添加邮箱账户

```bash
curl -X POST http://localhost:8000/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "refresh_token": "your-refresh-token"
  }'
```

### 获取邮件

```bash
# 获取收件箱最新 5 封邮件
curl "http://localhost:8000/api/mail/<EMAIL>?folder=inbox&limit=5"

# 获取垃圾邮件文件夹
curl "http://localhost:8000/api/mail/<EMAIL>?folder=junk&limit=3"
```

### 查看所有账户

```bash
curl http://localhost:8000/api/accounts
```

### 删除账户

```bash
curl -X DELETE http://localhost:8000/api/accounts/<EMAIL>
```

## 📊 测试结果

### 账户管理测试
- ✅ 添加账户: 201 Created
- ✅ 重复添加: 409 Conflict (正确处理)
- ✅ 查询账户: 200 OK
- ✅ 删除账户: 200 OK
- ✅ 查询不存在账户: 404 Not Found

### 邮件获取测试
- ✅ 成功获取邮件: 200 OK
- ✅ Token 失效处理: 401 Unauthorized
- ✅ 登录失败处理: 403 Forbidden
- ✅ 参数验证: 400 Bad Request
- ✅ 不存在账户: 404 Not Found

### 真实账户测试
- ✅ 成功添加 5 个真实邮箱账户
- ✅ 成功获取真实邮件数据
- ✅ 正确解析邮件内容 (主题、发件人、时间、正文)

## 📋 数据库结构

```sql
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_address TEXT UNIQUE NOT NULL,
    client_id TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    status TEXT DEFAULT 'OK',
    last_updated TEXT NOT NULL
);
```

## 🔐 安全考虑

1. **敏感数据保护**: API 响应中不返回 refresh_token
2. **参数验证**: 严格验证所有输入参数
3. **错误处理**: 不暴露内部系统信息
4. **状态监控**: 实时更新账户状态

## 📈 性能特性

- **连接复用**: 高效的数据库连接管理
- **错误恢复**: 自动处理 Token 失效和重新认证
- **日志记录**: 详细的操作日志便于调试
- **参数限制**: 防止过大的邮件获取请求

## 🚨 注意事项

1. **Token 管理**: refresh_token 有效期有限，需要定期更新
2. **频率控制**: 避免过于频繁的邮件获取请求
3. **文件夹支持**: 不同邮箱服务商的文件夹名称可能不同
4. **网络超时**: 邮件获取可能需要较长时间

## 📚 详细文档

查看 [API_DOCUMENTATION.md](./API_DOCUMENTATION.md) 获取完整的 API 文档和使用说明。

## 🧪 测试

```bash
# 测试数据库功能
python test_database.py

# 测试 API 接口
python test_api.py

# 测试邮件获取 (需要真实账户)
python test_mail_api.py

# 批量添加真实账户
python add_real_accounts.py
```

## 📝 开发日志

### 已完成功能
1. ✅ 数据库设计和操作模块
2. ✅ 邮件获取核心逻辑封装
3. ✅ 账户管理 API 接口
4. ✅ 邮件获取 API 接口
5. ✅ 完整的错误处理机制
6. ✅ 真实账户测试验证

### 技术决策
- 使用 Flask 替代 FastAPI (解决 Python 3.13 兼容性问题)
- 简化 Pydantic 模型定义 (避免版本冲突)
- 采用 SQLite 数据库 (简化部署)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License
