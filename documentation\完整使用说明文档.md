# 多账户邮件聚合 API 服务 - 完整使用说明文档

## 📋 目录
1. [项目概述](#项目概述)
2. [系统要求](#系统要求)
3. [安装配置](#安装配置)
4. [启动服务](#启动服务)
5. [API 接口详解](#api-接口详解)
6. [使用示例](#使用示例)
7. [数据库管理](#数据库管理)
8. [故障排除](#故障排除)
9. [安全注意事项](#安全注意事项)
10. [维护和监控](#维护和监控)

---

## 📖 项目概述

### 功能描述
这是一个基于 Flask 的 RESTful API 服务，专门用于管理多个微软邮箱账户并通过 API 接口获取邮件。该服务无需前端界面，纯粹作为后端 API 供其他程序调用。

### 核心特性
- ✅ **多账户管理**: 支持添加、查询、删除多个邮箱账户
- ✅ **邮件获取**: 通过 API 获取指定账户的邮件内容
- ✅ **多文件夹支持**: 支持收件箱、垃圾邮件、已发送等文件夹
- ✅ **自动认证**: 自动使用 refresh_token 获取 access_token
- ✅ **状态监控**: 实时监控账户状态和连接状态
- ✅ **安全设计**: 响应中不包含敏感的 refresh_token 信息

### 技术架构
- **Web 框架**: Flask 3.1.1
- **数据库**: SQLite (本地文件数据库)
- **认证方式**: Microsoft OAuth2 + IMAP XOAUTH2
- **邮件协议**: IMAP (outlook.live.com)

---

## 💻 系统要求

### 硬件要求
- **CPU**: 1 核心以上
- **内存**: 512MB 以上
- **存储**: 100MB 可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11, Linux, macOS
- **Python**: 3.8 或更高版本 (已测试 Python 3.13)
- **网络端口**: 8000 端口可用

### 依赖包
```
flask>=3.1.1
requests>=2.31.0
```

---

## 🔧 安装配置

### 1. 环境准备

#### 检查 Python 版本
```bash
python --version
# 应显示 Python 3.8+ 版本
```

#### 安装依赖包
```bash
# 方法1: 使用 requirements.txt
pip install -r requirements.txt

# 方法2: 手动安装
pip install flask requests
```

### 2. 文件结构确认

确保以下文件存在于项目目录中：
```
/API批量取件/
├── flask_main.py          # 主服务器文件
├── database.py            # 数据库操作模块
├── mail_fetcher.py        # 邮件获取模块
├── models.py              # 数据模型
├── requirements.txt       # 依赖列表
├── accounts.db            # 数据库文件 (自动创建)
└── 完整使用说明文档.md    # 本文档
```

### 3. 权限设置

#### Windows
- 确保 Python 脚本有执行权限
- 防火墙允许 8000 端口访问

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x flask_main.py

# 检查端口是否被占用
netstat -an | grep 8000
```

---

## 🚀 启动服务

### 1. 启动命令

#### 标准启动
```bash
cd /path/to/API批量取件
python flask_main.py
```

#### 后台运行 (Linux/macOS)
```bash
nohup python flask_main.py > server.log 2>&1 &
```

#### Windows 后台运行
```cmd
start /B python flask_main.py
```

### 2. 启动成功标志

看到以下输出表示启动成功：
```
INFO:database:数据库初始化完成
INFO:__main__:数据库初始化完成
INFO:__main__:启动 Flask 应用...
 * Serving Flask app 'flask_main'
 * Debug mode: on
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://**************:8000
INFO:werkzeug:Press CTRL+C to quit
```

### 3. 验证服务

#### 浏览器验证
访问: `http://localhost:8000`

应看到：
```json
{
    "message": "多账户邮件聚合 API 服务",
    "status": "运行中",
    "version": "1.0.0",
    "endpoints": {
        "accounts": "/api/accounts",
        "docs": "请查看源代码了解 API 接口"
    }
}
```

#### 命令行验证
```bash
curl http://localhost:8000
```

---

## 🔌 API 接口详解

### 基础信息
- **服务地址**: `http://localhost:8000`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1. 服务状态接口

#### GET /
**功能**: 获取 API 服务状态
**参数**: 无
**返回**: 服务基本信息

```bash
curl http://localhost:8000/
```

### 2. 账户管理接口

#### POST /api/accounts - 添加账户
**功能**: 添加新的邮箱账户到数据库

**请求格式**:
```json
{
    "email_address": "邮箱地址",
    "client_id": "微软应用的Client ID", 
    "refresh_token": "OAuth2 Refresh Token"
}
```

**成功响应** (201):
```json
{
    "id": 1,
    "email_address": "<EMAIL>",
    "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
    "status": "OK",
    "last_updated": "2025-08-01T12:00:00.000000"
}
```

**错误响应** (409 - 账户已存在):
```json
{
    "error": "ACCOUNT_EXISTS",
    "message": "邮箱地址 <EMAIL> 已存在",
    "status_code": 409
}
```

#### GET /api/accounts - 获取所有账户
**功能**: 获取数据库中所有账户信息 (不包含 refresh_token)

```bash
curl http://localhost:8000/api/accounts
```

#### GET /api/accounts/{email} - 获取指定账户
**功能**: 获取指定邮箱的账户信息

```bash
curl http://localhost:8000/api/accounts/<EMAIL>
```

#### DELETE /api/accounts/{email} - 删除账户
**功能**: 从数据库中删除指定账户

```bash
curl -X DELETE http://localhost:8000/api/accounts/<EMAIL>
```

### 3. 邮件获取接口 (核心功能)

#### GET /api/mail/{email} - 获取邮件
**功能**: 获取指定账户的邮件内容

**查询参数**:
- `folder` (可选): 邮箱文件夹，默认 'inbox'
  - 支持值: `inbox`, `junk`, `sent`, `drafts`, `deleted`
- `limit` (可选): 获取邮件数量，默认 5，范围 1-50

**请求示例**:
```bash
# 获取收件箱最新 5 封邮件
curl "http://localhost:8000/api/mail/<EMAIL>"

# 获取收件箱最新 10 封邮件
curl "http://localhost:8000/api/mail/<EMAIL>?limit=10"

# 获取垃圾邮件文件夹最新 3 封邮件
curl "http://localhost:8000/api/mail/<EMAIL>?folder=junk&limit=3"
```

**成功响应** (200):
```json
{
    "email_address": "<EMAIL>",
    "folder": "inbox",
    "total_count": 2,
    "messages": [
        {
            "subject": "连接到 Microsoft 帐户的新应用",
            "sender": "Microsoft 帐户团队 (<EMAIL>)",
            "recipient": "<EMAIL>",
            "date": "2025-07-01 19:04:01",
            "body": "完整的邮件正文内容..."
        },
        {
            "subject": "欢迎使用你的新 Outlook.com 帐户",
            "sender": "Outlook 团队 (<EMAIL>)",
            "recipient": "<EMAIL>", 
            "date": "2025-07-02 10:03:48",
            "body": "完整的邮件正文内容..."
        }
    ]
}
```

**错误响应**:

**Token 失效** (401):
```json
{
    "error": "TOKEN失效",
    "message": "Token 获取失败: invalid_grant",
    "email_address": "<EMAIL>",
    "status_code": 401
}
```

**登录失败** (403):
```json
{
    "error": "IMAP错误", 
    "message": "IMAP 连接或操作失败: authentication failed",
    "email_address": "<EMAIL>",
    "status_code": 403
}
```

**参数错误** (400):
```json
{
    "error": "INVALID_LIMIT",
    "message": "limit 参数必须在 1-50 之间",
    "status_code": 400
}
```

**账户不存在** (404):
```json
{
    "error": "ACCOUNT_NOT_FOUND",
    "message": "邮箱地址 <EMAIL> 不存在", 
    "status_code": 404
}
```

---

## 💡 使用示例

### Python 示例

#### 完整的使用流程
```python
import requests
import json

BASE_URL = "http://localhost:8000"

# 1. 检查服务状态
def check_service():
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✓ 服务运行正常")
            return True
        else:
            print("✗ 服务异常")
            return False
    except:
        print("✗ 无法连接到服务")
        return False

# 2. 添加邮箱账户
def add_account(email, client_id, refresh_token):
    account_data = {
        "email_address": email,
        "client_id": client_id,
        "refresh_token": refresh_token
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/accounts",
            json=account_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            print(f"✓ 成功添加账户: {email}")
            return response.json()
        elif response.status_code == 409:
            print(f"⚠ 账户已存在: {email}")
            return None
        else:
            print(f"✗ 添加失败: {response.json()}")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

# 3. 获取邮件
def get_emails(email, folder="inbox", limit=5):
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{email}",
            params={"folder": folder, "limit": limit},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 成功获取 {result['total_count']} 封邮件")
            
            for i, msg in enumerate(result['messages'], 1):
                print(f"\n--- 邮件 {i} ---")
                print(f"主题: {msg['subject']}")
                print(f"发件人: {msg['sender']}")
                print(f"时间: {msg['date']}")
                print(f"正文预览: {msg['body'][:100]}...")
            
            return result
        else:
            error = response.json()
            print(f"✗ 获取邮件失败: {error['message']}")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

# 4. 查看所有账户
def list_accounts():
    try:
        response = requests.get(f"{BASE_URL}/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            print(f"数据库中共有 {len(accounts)} 个账户:")
            for acc in accounts:
                print(f"  - {acc['email_address']} (状态: {acc['status']})")
            return accounts
        else:
            print("✗ 获取账户列表失败")
            return None
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return None

# 使用示例
if __name__ == "__main__":
    # 检查服务
    if not check_service():
        exit(1)
    
    # 添加账户 (请替换为真实信息)
    email = "<EMAIL>"
    client_id = "your-client-id"
    refresh_token = "your-refresh-token"
    
    add_account(email, client_id, refresh_token)
    
    # 查看所有账户
    list_accounts()
    
    # 获取邮件
    get_emails(email, "inbox", 3)
```

### cURL 示例

#### 批量操作脚本 (bash)
```bash
#!/bin/bash

BASE_URL="http://localhost:8000"

# 检查服务状态
echo "检查服务状态..."
curl -s $BASE_URL/ | jq .

# 添加账户
echo -e "\n添加账户..."
curl -X POST $BASE_URL/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "refresh_token": "your-refresh-token"
  }' | jq .

# 查看所有账户
echo -e "\n查看所有账户..."
curl -s $BASE_URL/api/accounts | jq .

# 获取邮件
echo -e "\n获取邮件..."
curl -s "$BASE_URL/api/mail/<EMAIL>?folder=inbox&limit=3" | jq .

# 删除账户
echo -e "\n删除账户..."
curl -X DELETE $BASE_URL/api/accounts/<EMAIL> | jq .
```

---

## 🗄️ 数据库管理

### 数据库文件
- **位置**: `./accounts.db`
- **类型**: SQLite 3
- **大小**: 通常 < 1MB

### 表结构
```sql
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email_address TEXT UNIQUE NOT NULL,
    client_id TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    status TEXT DEFAULT 'OK',
    last_updated TEXT NOT NULL
);
```

### 字段说明
- `id`: 自增主键
- `email_address`: 邮箱地址 (唯一)
- `client_id`: 微软应用 Client ID
- `refresh_token`: OAuth2 刷新令牌 (敏感信息)
- `status`: 账户状态 (`OK`, `Token Invalid`, `Login Failed`, `Error`)
- `last_updated`: 最后更新时间 (ISO 8601 格式)

### 数据库操作

#### 直接查看数据库
```bash
# 安装 sqlite3 (如果没有)
# Ubuntu/Debian: sudo apt install sqlite3
# macOS: brew install sqlite3
# Windows: 下载 sqlite3.exe

# 连接数据库
sqlite3 accounts.db

# 查看表结构
.schema accounts

# 查看所有账户
SELECT id, email_address, status, last_updated FROM accounts;

# 退出
.quit
```

#### 备份数据库
```bash
# 备份
cp accounts.db accounts_backup_$(date +%Y%m%d_%H%M%S).db

# 恢复
cp accounts_backup_20250801_120000.db accounts.db
```

#### 清空数据库
```bash
# 方法1: 删除文件 (服务会重新创建)
rm accounts.db

# 方法2: 清空表
sqlite3 accounts.db "DELETE FROM accounts;"
```

---

## 🔧 故障排除

### 常见问题

#### 1. 服务无法启动

**问题**: `ModuleNotFoundError: No module named 'flask'`
**解决**:
```bash
pip install flask requests
```

**问题**: `Address already in use`
**解决**:
```bash
# 查找占用 8000 端口的进程
netstat -an | grep 8000
# 或
lsof -i :8000

# 杀死进程
kill -9 <PID>
```

#### 2. 邮件获取失败

**问题**: Token 失效 (401 错误)
**原因**: refresh_token 过期
**解决**: 重新获取 refresh_token 并更新账户

**问题**: IMAP 登录失败 (403 错误)
**原因**: 
- 账户被锁定
- 网络连接问题
- IMAP 服务器问题
**解决**: 
- 检查账户状态
- 检查网络连接
- 稍后重试

**问题**: 文件夹不存在
**原因**: 指定的文件夹名称不正确
**解决**: 使用正确的文件夹名称 (`inbox`, `junk`, `sent`, `drafts`, `deleted`)

#### 3. 数据库问题

**问题**: `database is locked`
**解决**:
```bash
# 检查是否有多个服务实例运行
ps aux | grep flask_main.py

# 重启服务
pkill -f flask_main.py
python flask_main.py
```

**问题**: 数据库损坏
**解决**:
```bash
# 检查数据库完整性
sqlite3 accounts.db "PRAGMA integrity_check;"

# 如果损坏，从备份恢复
cp accounts_backup_latest.db accounts.db
```

### 日志分析

#### 查看实时日志
```bash
# 如果使用 nohup 启动
tail -f server.log

# 如果直接运行，查看控制台输出
```

#### 常见日志信息

**正常启动**:
```
INFO:database:数据库初始化完成
INFO:__main__:启动 Flask 应用...
```

**成功获取邮件**:
```
INFO:mail_fetcher:成功获取 access_token
INFO:mail_fetcher:成功获取 3 封邮件
INFO:database:更新账户状态: <EMAIL> -> OK
```

**Token 失效**:
```
ERROR:mail_fetcher:获取 access_token 失败: invalid_grant
INFO:database:更新账户状态: <EMAIL> -> Token Invalid
```

**IMAP 错误**:
```
ERROR:mail_fetcher:IMAP 错误: authentication failed
INFO:database:更新账户状态: <EMAIL> -> Login Failed
```

### 性能优化

#### 1. 数据库优化
```sql
-- 创建索引 (如果需要)
CREATE INDEX idx_email ON accounts(email_address);
CREATE INDEX idx_status ON accounts(status);
```

#### 2. 内存优化
- 限制同时处理的邮件获取请求数量
- 定期清理日志文件
- 监控内存使用情况

#### 3. 网络优化
- 设置合适的超时时间
- 实现请求重试机制
- 使用连接池

---

## 🔒 安全注意事项

### 数据安全

#### 1. 敏感信息保护
- ✅ API 响应中不返回 `refresh_token`
- ✅ 数据库文件权限设置为 600
- ⚠️ 定期更换 refresh_token
- ⚠️ 不要在日志中记录敏感信息

#### 2. 访问控制
```bash
# 设置数据库文件权限 (Linux/macOS)
chmod 600 accounts.db

# 限制服务只监听本地地址
# 在 flask_main.py 中修改:
# app.run(host="127.0.0.1", port=8000)
```

#### 3. 网络安全
- 🔒 在生产环境中使用 HTTPS
- 🔒 配置防火墙规则
- 🔒 使用反向代理 (如 nginx)

### 生产环境部署

#### 1. 使用 WSGI 服务器
```bash
# 安装 gunicorn
pip install gunicorn

# 启动服务
gunicorn -w 4 -b 0.0.0.0:8000 flask_main:app
```

#### 2. 配置反向代理 (nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 3. 系统服务配置 (systemd)
```ini
# /etc/systemd/system/mail-api.service
[Unit]
Description=Mail API Service
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/API批量取件
ExecStart=/usr/bin/python3 flask_main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

---

## 📊 维护和监控

### 日常维护

#### 1. 定期检查
- 每日检查服务状态
- 每周检查账户状态
- 每月备份数据库

#### 2. 状态监控脚本
```python
import requests
import sqlite3
from datetime import datetime

def check_service_health():
    """检查服务健康状态"""
    try:
        # 检查 API 服务
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code != 200:
            print(f"⚠️ API 服务异常: {response.status_code}")
            return False
        
        # 检查数据库
        conn = sqlite3.connect("accounts.db")
        cursor = conn.execute("SELECT COUNT(*) FROM accounts")
        count = cursor.fetchone()[0]
        conn.close()
        
        print(f"✅ 服务正常 - {count} 个账户")
        return True
        
    except Exception as e:
        print(f"❌ 服务检查失败: {e}")
        return False

def check_account_status():
    """检查账户状态"""
    try:
        response = requests.get("http://localhost:8000/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            
            ok_count = sum(1 for acc in accounts if acc['status'] == 'OK')
            error_count = len(accounts) - ok_count
            
            print(f"账户状态: {ok_count} 正常, {error_count} 异常")
            
            # 显示异常账户
            for acc in accounts:
                if acc['status'] != 'OK':
                    print(f"  ⚠️ {acc['email_address']}: {acc['status']}")
                    
    except Exception as e:
        print(f"账户状态检查失败: {e}")

if __name__ == "__main__":
    print(f"=== 服务健康检查 - {datetime.now()} ===")
    check_service_health()
    check_account_status()
```

#### 3. 自动备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp accounts.db $BACKUP_DIR/accounts_$DATE.db

# 保留最近 30 天的备份
find $BACKUP_DIR -name "accounts_*.db" -mtime +30 -delete

echo "备份完成: accounts_$DATE.db"
```

### 性能监控

#### 1. 资源使用监控
```bash
# CPU 和内存使用
ps aux | grep flask_main.py

# 磁盘使用
du -sh accounts.db

# 网络连接
netstat -an | grep 8000
```

#### 2. API 性能监控
```python
import time
import requests

def test_api_performance():
    """测试 API 性能"""
    start_time = time.time()
    
    try:
        response = requests.get("http://localhost:8000/api/accounts")
        end_time = time.time()
        
        if response.status_code == 200:
            response_time = (end_time - start_time) * 1000
            print(f"API 响应时间: {response_time:.2f}ms")
        else:
            print(f"API 错误: {response.status_code}")
            
    except Exception as e:
        print(f"API 测试失败: {e}")

# 定期执行
test_api_performance()
```

---

## 📞 技术支持

### 联系方式
- 查看项目文档: `README.md`
- API 详细文档: `API_DOCUMENTATION.md`
- 问题反馈: 通过项目 Issue 提交

### 常用命令速查

```bash
# 启动服务
python flask_main.py

# 检查服务状态
curl http://localhost:8000/

# 查看所有账户
curl http://localhost:8000/api/accounts

# 获取邮件
curl "http://localhost:8000/api/mail/<EMAIL>?limit=5"

# 备份数据库
cp accounts.db accounts_backup.db

# 查看日志 (如果使用 nohup)
tail -f server.log
```

---

---

## 📚 附录

### A. 微软 OAuth2 配置指南

#### 1. 创建 Azure 应用
1. 访问 [Azure Portal](https://portal.azure.com)
2. 进入 "Azure Active Directory" > "应用注册"
3. 点击 "新注册"
4. 填写应用信息:
   - 名称: 自定义应用名称
   - 支持的账户类型: 任何组织目录中的账户和个人 Microsoft 账户
   - 重定向 URI: `http://localhost` (用于获取授权码)

#### 2. 配置 API 权限
1. 在应用页面，点击 "API 权限"
2. 添加权限 > Microsoft Graph > 委托的权限
3. 添加以下权限:
   - `IMAP.AccessAsUser.All`
   - `Mail.Read`
   - `offline_access`
4. 点击 "授予管理员同意"

#### 3. 获取 Client ID
在应用概述页面复制 "应用程序(客户端) ID"

#### 4. 获取 Refresh Token
```python
# 获取授权码的 URL (在浏览器中访问)
auth_url = f"""https://login.microsoftonline.com/common/oauth2/v2.0/authorize?
client_id={CLIENT_ID}&
response_type=code&
redirect_uri=http://localhost&
scope=https://outlook.live.com/IMAP.AccessAsUser.All offline_access&
response_mode=query"""

# 用户授权后，从重定向 URL 中获取 code 参数
# 然后使用 code 换取 refresh_token
import requests

token_data = {
    'client_id': CLIENT_ID,
    'code': AUTHORIZATION_CODE,
    'redirect_uri': 'http://localhost',
    'grant_type': 'authorization_code',
    'scope': 'https://outlook.live.com/IMAP.AccessAsUser.All offline_access'
}

response = requests.post(
    'https://login.microsoftonline.com/common/oauth2/v2.0/token',
    data=token_data
)

tokens = response.json()
refresh_token = tokens['refresh_token']
```

### B. 文件夹名称对照表

| 中文名称 | API 参数 | IMAP 文件夹名 |
|---------|----------|---------------|
| 收件箱   | inbox    | INBOX         |
| 垃圾邮件 | junk     | Junk          |
| 已发送   | sent     | Sent Items    |
| 草稿箱   | drafts   | Drafts        |
| 已删除   | deleted  | Deleted Items |

### C. 错误代码对照表

| HTTP 状态码 | 错误类型 | 说明 | 解决方案 |
|------------|----------|------|----------|
| 200 | 成功 | 请求成功 | - |
| 201 | 创建成功 | 账户添加成功 | - |
| 400 | 参数错误 | 请求参数不正确 | 检查参数格式和范围 |
| 401 | Token失效 | refresh_token 过期 | 重新获取 refresh_token |
| 403 | 认证失败 | IMAP 登录失败 | 检查账户状态和网络 |
| 404 | 资源不存在 | 账户或邮件不存在 | 确认账户已添加 |
| 409 | 资源冲突 | 账户已存在 | 使用现有账户或删除后重新添加 |
| 500 | 服务器错误 | 内部错误 | 查看日志，重启服务 |

### D. 测试用例模板

#### 功能测试清单
```
□ 服务启动测试
  □ 正常启动
  □ 端口占用处理
  □ 数据库初始化

□ 账户管理测试
  □ 添加新账户
  □ 重复添加处理
  □ 查询账户信息
  □ 删除账户
  □ 查询不存在账户

□ 邮件获取测试
  □ 正常获取邮件
  □ 不同文件夹测试
  □ 参数边界测试
  □ Token 失效处理
  □ 网络异常处理

□ 错误处理测试
  □ 无效参数
  □ 网络超时
  □ 数据库错误
  □ IMAP 错误
```

### E. 部署检查清单

#### 生产环境部署前检查
```
□ 环境准备
  □ Python 版本确认
  □ 依赖包安装
  □ 端口可用性检查
  □ 防火墙配置

□ 安全配置
  □ 数据库文件权限
  □ 服务监听地址
  □ 日志敏感信息过滤
  □ HTTPS 配置

□ 性能配置
  □ WSGI 服务器配置
  □ 反向代理配置
  □ 资源限制设置
  □ 监控配置

□ 备份策略
  □ 数据库备份
  □ 配置文件备份
  □ 恢复流程测试
```

---

**文档版本**: v1.0
**最后更新**: 2025-08-01
**适用版本**: 多账户邮件聚合 API 服务 v1.0.0

---

## 🎯 快速开始指南 (5分钟上手)

### 第一步: 启动服务
```bash
cd /path/to/API批量取件
python flask_main.py
```

### 第二步: 验证服务
```bash
curl http://localhost:8000/
```

### 第三步: 添加账户
```bash
curl -X POST http://localhost:8000/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "refresh_token": "your-refresh-token"
  }'
```

### 第四步: 获取邮件
```bash
curl "http://localhost:8000/api/mail/<EMAIL>?limit=3"
```

**🎉 恭喜！你已经成功运行了多账户邮件聚合 API 服务！**
