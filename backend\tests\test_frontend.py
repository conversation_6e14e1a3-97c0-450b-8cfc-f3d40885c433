"""
前端功能测试脚本
验证前端应用是否能正确调用后端 API
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_backend_api():
    """测试后端 API 是否正常"""
    print("1. 测试后端 API 连接...")
    
    try:
        # 测试根路径
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端服务正常运行")
        else:
            print(f"   ❌ 后端服务异常: {response.status_code}")
            return False
            
        # 测试账户接口
        response = requests.get("http://localhost:8000/api/accounts", timeout=5)
        if response.status_code == 200:
            accounts = response.json()
            print(f"   ✅ 账户接口正常，共 {len(accounts)} 个账户")
        else:
            print(f"   ❌ 账户接口异常: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ 后端连接失败: {e}")
        return False

def test_frontend_basic():
    """测试前端基本功能"""
    print("\n2. 测试前端基本功能...")
    
    try:
        # 配置 Chrome 选项
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 打开前端页面
        frontend_url = "file:///c:/Users/<USER>/Desktop/API批量取件/frontend/index.html"
        driver.get(frontend_url)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 检查页面标题
        title = driver.title
        print(f"   ✅ 页面标题: {title}")
        
        # 检查主要元素是否存在
        try:
            app_title = wait.until(EC.presence_of_element_located((By.CLASS_NAME, "app-title")))
            print(f"   ✅ 应用标题: {app_title.text}")
        except:
            print("   ❌ 应用标题元素未找到")
        
        try:
            fetch_btn = driver.find_element(By.ID, "fetchEmailsBtn")
            print(f"   ✅ 获取邮件按钮: {fetch_btn.text}")
        except:
            print("   ❌ 获取邮件按钮未找到")
        
        try:
            clear_btn = driver.find_element(By.ID, "clearListBtn")
            print(f"   ✅ 清空列表按钮: {clear_btn.text}")
        except:
            print("   ❌ 清空列表按钮未找到")
        
        # 检查状态消息
        try:
            status_msg = driver.find_element(By.ID, "statusMessage")
            print(f"   ✅ 状态消息: {status_msg.text}")
        except:
            print("   ❌ 状态消息元素未找到")
        
        # 等待 JavaScript 初始化
        time.sleep(3)
        
        # 检查账户列表是否加载
        try:
            accounts_list = driver.find_element(By.ID, "accountsList")
            account_items = accounts_list.find_elements(By.CLASS_NAME, "account-item")
            print(f"   ✅ 账户列表加载完成，共 {len(account_items)} 个账户")
        except:
            print("   ❌ 账户列表加载失败")
        
        # 检查文件夹选项
        try:
            folder_checkboxes = driver.find_elements(By.CLASS_NAME, "folder-checkbox")
            print(f"   ✅ 文件夹选项: {len(folder_checkboxes)} 个")
        except:
            print("   ❌ 文件夹选项未找到")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ 前端测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def test_frontend_interaction():
    """测试前端交互功能"""
    print("\n3. 测试前端交互功能...")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        frontend_url = "file:///c:/Users/<USER>/Desktop/API批量取件/frontend/index.html"
        driver.get(frontend_url)
        
        wait = WebDriverWait(driver, 10)
        
        # 等待页面初始化
        time.sleep(3)
        
        # 测试全选账户功能
        try:
            select_all = driver.find_element(By.ID, "selectAllAccounts")
            select_all.click()
            print("   ✅ 全选账户功能可点击")
        except Exception as e:
            print(f"   ❌ 全选账户功能失败: {e}")
        
        # 测试文件夹选择
        try:
            folder_checkboxes = driver.find_elements(By.CLASS_NAME, "folder-checkbox")
            if folder_checkboxes:
                folder_checkboxes[0].click()  # 点击第一个文件夹
                print("   ✅ 文件夹选择功能可点击")
            else:
                print("   ❌ 未找到文件夹复选框")
        except Exception as e:
            print(f"   ❌ 文件夹选择功能失败: {e}")
        
        # 测试获取邮件按钮
        try:
            fetch_btn = driver.find_element(By.ID, "fetchEmailsBtn")
            fetch_btn.click()
            print("   ✅ 获取邮件按钮可点击")
            
            # 等待一下看是否有响应
            time.sleep(2)
            
            # 检查状态消息是否更新
            status_msg = driver.find_element(By.ID, "statusMessage")
            print(f"   ✅ 点击后状态消息: {status_msg.text}")
            
        except Exception as e:
            print(f"   ❌ 获取邮件按钮测试失败: {e}")
        
        # 测试清空列表按钮
        try:
            clear_btn = driver.find_element(By.ID, "clearListBtn")
            clear_btn.click()
            print("   ✅ 清空列表按钮可点击")
        except Exception as e:
            print(f"   ❌ 清空列表按钮测试失败: {e}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"   ❌ 交互测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def main():
    """主测试函数"""
    print("🚀 开始前端功能测试")
    print("=" * 50)
    
    # 测试后端 API
    backend_ok = test_backend_api()
    
    if not backend_ok:
        print("\n❌ 后端服务异常，无法继续测试前端")
        return
    
    # 测试前端基本功能
    frontend_basic_ok = test_frontend_basic()
    
    # 测试前端交互功能
    frontend_interaction_ok = test_frontend_interaction()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   后端 API: {'✅ 正常' if backend_ok else '❌ 异常'}")
    print(f"   前端基本功能: {'✅ 正常' if frontend_basic_ok else '❌ 异常'}")
    print(f"   前端交互功能: {'✅ 正常' if frontend_interaction_ok else '❌ 异常'}")
    
    if backend_ok and frontend_basic_ok and frontend_interaction_ok:
        print("\n🎉 所有测试通过！前端应用可以正常使用")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
