"""
数据库操作模块
负责 SQLite 数据库的连接、表创建和所有 CRUD 操作
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from models import AccountCreate, AccountDetail

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库文件路径
DATABASE_PATH = "accounts.db"


@contextmanager
def get_db_connection():
    """数据库连接上下文管理器"""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"数据库操作错误: {e}")
        raise
    finally:
        if conn:
            conn.close()


def init_db() -> None:
    """
    初始化数据库，创建 accounts 表（如果不存在）
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email_address TEXT UNIQUE NOT NULL,
        client_id TEXT NOT NULL,
        refresh_token TEXT NOT NULL,
        status TEXT DEFAULT 'OK',
        last_updated TEXT NOT NULL
    )
    """
    
    try:
        with get_db_connection() as conn:
            conn.execute(create_table_sql)
            conn.commit()
            logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def add_account(account_data: AccountCreate) -> AccountDetail:
    """
    添加新的邮箱账户
    
    Args:
        account_data: 账户创建数据
        
    Returns:
        AccountDetail: 创建的账户详细信息
        
    Raises:
        sqlite3.IntegrityError: 如果邮箱地址已存在
    """
    current_time = datetime.now().isoformat()
    
    insert_sql = """
    INSERT INTO accounts (email_address, client_id, refresh_token, status, last_updated)
    VALUES (?, ?, ?, ?, ?)
    """
    
    with get_db_connection() as conn:
        cursor = conn.execute(
            insert_sql,
            (account_data.email_address, account_data.client_id, 
             account_data.refresh_token, 'OK', current_time)
        )
        account_id = cursor.lastrowid
        conn.commit()
        
        logger.info(f"成功添加账户: {account_data.email_address}")
        
        return AccountDetail(
            id=account_id,
            email_address=account_data.email_address,
            client_id=account_data.client_id,
            refresh_token=account_data.refresh_token,
            status='OK',
            last_updated=current_time
        )


def get_account_by_email(email_address: str) -> Optional[AccountDetail]:
    """
    根据邮箱地址获取账户信息
    
    Args:
        email_address: 邮箱地址
        
    Returns:
        AccountDetail 或 None
    """
    select_sql = """
    SELECT id, email_address, client_id, refresh_token, status, last_updated
    FROM accounts WHERE email_address = ?
    """
    
    with get_db_connection() as conn:
        cursor = conn.execute(select_sql, (email_address,))
        row = cursor.fetchone()
        
        if row:
            return AccountDetail(
                id=row['id'],
                email_address=row['email_address'],
                client_id=row['client_id'],
                refresh_token=row['refresh_token'],
                status=row['status'],
                last_updated=row['last_updated']
            )
        return None


def get_all_accounts() -> List[Dict[str, Any]]:
    """
    获取所有账户信息（不包含 refresh_token）
    
    Returns:
        List[Dict]: 账户列表
    """
    select_sql = """
    SELECT id, email_address, client_id, status, last_updated
    FROM accounts ORDER BY last_updated DESC
    """
    
    with get_db_connection() as conn:
        cursor = conn.execute(select_sql)
        rows = cursor.fetchall()
        
        return [dict(row) for row in rows]


def delete_account(email_address: str) -> bool:
    """
    删除指定邮箱的账户
    
    Args:
        email_address: 邮箱地址
        
    Returns:
        bool: 是否成功删除
    """
    delete_sql = "DELETE FROM accounts WHERE email_address = ?"
    
    with get_db_connection() as conn:
        cursor = conn.execute(delete_sql, (email_address,))
        conn.commit()
        
        if cursor.rowcount > 0:
            logger.info(f"成功删除账户: {email_address}")
            return True
        else:
            logger.warning(f"账户不存在: {email_address}")
            return False


def update_account_status(email_address: str, status: str) -> bool:
    """
    更新账户状态
    
    Args:
        email_address: 邮箱地址
        status: 新状态
        
    Returns:
        bool: 是否成功更新
    """
    current_time = datetime.now().isoformat()
    update_sql = """
    UPDATE accounts 
    SET status = ?, last_updated = ?
    WHERE email_address = ?
    """
    
    with get_db_connection() as conn:
        cursor = conn.execute(update_sql, (status, current_time, email_address))
        conn.commit()
        
        if cursor.rowcount > 0:
            logger.info(f"更新账户状态: {email_address} -> {status}")
            return True
        return False
