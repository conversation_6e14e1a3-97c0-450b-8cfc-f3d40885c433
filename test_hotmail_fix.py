"""
测试hotmail账户修复
验证修复后的hotmail账户是否能正常工作
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_hotmail_accounts():
    """测试hotmail账户的邮件获取"""
    print("🔧 测试hotmail账户修复效果")
    print("=" * 60)
    
    # 获取所有账户
    print("1. 获取当前账户列表...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            print(f"   📊 总账户数: {len(accounts)}")
            
            # 分类账户
            outlook_accounts = [acc for acc in accounts if acc['email_address'].endswith('@outlook.com')]
            hotmail_accounts = [acc for acc in accounts if acc['email_address'].endswith('@hotmail.com')]
            
            print(f"   📧 Outlook账户: {len(outlook_accounts)} 个")
            print(f"   📧 Hotmail账户: {len(hotmail_accounts)} 个")
            
            # 显示账户状态
            print("\n2. 账户状态分析:")
            for acc in accounts[:10]:  # 只显示前10个
                status_icon = "✅" if acc['status'] == 'OK' else "❌"
                domain = acc['email_address'].split('@')[1]
                print(f"   {status_icon} {acc['email_address']} ({domain}) - {acc['status']}")
            
            if len(accounts) > 10:
                print(f"   ... 还有 {len(accounts) - 10} 个账户")
                
        else:
            print(f"   ❌ 获取账户失败: {response.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return

def test_single_hotmail_account():
    """测试单个hotmail账户"""
    print("\n3. 测试单个hotmail账户邮件获取:")
    print("=" * 50)
    
    # 获取一个hotmail账户进行测试
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            hotmail_accounts = [acc for acc in accounts if acc['email_address'].endswith('@hotmail.com')]
            
            if not hotmail_accounts:
                print("   ⚠️ 没有hotmail账户可测试")
                return
                
            test_account = hotmail_accounts[0]
            email = test_account['email_address']
            
            print(f"   🎯 测试账户: {email}")
            print(f"   📊 当前状态: {test_account['status']}")
            
            # 尝试获取邮件
            print("   🔄 正在获取邮件...")
            start_time = time.time()
            
            mail_response = requests.get(
                f"{API_BASE_URL}/api/mail/{email}?folders=inbox&limit=3",
                timeout=30
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"   ⏱️ 请求耗时: {duration:.2f}秒")
            print(f"   📊 响应状态: {mail_response.status_code}")
            
            if mail_response.status_code == 200:
                mail_data = mail_response.json()
                print(f"   ✅ 成功获取邮件!")
                print(f"   📧 邮件数量: {len(mail_data.get('messages', []))}")
                print(f"   📁 文件夹: {mail_data.get('folders', [])}")
                
                # 显示第一封邮件信息
                messages = mail_data.get('messages', [])
                if messages:
                    first_mail = messages[0]
                    print(f"   📄 第一封邮件:")
                    print(f"      主题: {first_mail.get('subject', 'N/A')[:50]}...")
                    print(f"      发件人: {first_mail.get('sender', 'N/A')}")
                    print(f"      时间: {first_mail.get('date', 'N/A')}")
                    
            elif mail_response.status_code == 401:
                print(f"   ❌ Token失效 (401)")
                print(f"   💡 建议: 需要更新refresh_token")
            elif mail_response.status_code == 403:
                print(f"   ❌ 权限不足 (403)")
                print(f"   💡 建议: 检查client_id和权限配置")
            else:
                print(f"   ❌ 获取失败: {mail_response.status_code}")
                try:
                    error_data = mail_response.json()
                    print(f"   📄 错误信息: {error_data}")
                except:
                    print(f"   📄 响应内容: {mail_response.text[:200]}...")
                    
        else:
            print(f"   ❌ 获取账户列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")

def test_batch_hotmail_processing():
    """测试批量hotmail处理"""
    print("\n4. 测试批量hotmail处理:")
    print("=" * 50)
    
    try:
        # 获取所有hotmail账户
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            hotmail_accounts = [acc['email_address'] for acc in accounts if acc['email_address'].endswith('@hotmail.com')]
            
            if not hotmail_accounts:
                print("   ⚠️ 没有hotmail账户可测试")
                return
                
            print(f"   📧 准备测试 {len(hotmail_accounts)} 个hotmail账户")
            
            # 批量获取邮件
            batch_data = {
                "accounts": hotmail_accounts[:5],  # 只测试前5个
                "folders": ["inbox"],
                "limit": 2
            }
            
            print("   🔄 正在批量获取邮件...")
            start_time = time.time()
            
            batch_response = requests.post(
                f"{API_BASE_URL}/api/mail/aggregate-async",
                json=batch_data,
                timeout=60
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"   ⏱️ 批量处理耗时: {duration:.2f}秒")
            print(f"   📊 响应状态: {batch_response.status_code}")
            
            if batch_response.status_code == 200:
                result = batch_response.json()
                print(f"   ✅ 批量处理完成!")
                print(f"   📧 总邮件数: {result.get('total_count', 0)}")
                print(f"   ✅ 成功账户: {len(result.get('successful_accounts', []))}")
                print(f"   ❌ 失败账户: {len(result.get('failed_accounts', []))}")
                
                # 显示成功的hotmail账户
                successful = result.get('successful_accounts', [])
                hotmail_successful = [acc for acc in successful if '@hotmail.com' in acc]
                if hotmail_successful:
                    print(f"   🎉 成功的hotmail账户: {len(hotmail_successful)} 个")
                    for acc in hotmail_successful[:3]:
                        print(f"      ✅ {acc}")
                        
            else:
                print(f"   ❌ 批量处理失败: {batch_response.status_code}")
                
        else:
            print(f"   ❌ 获取账户列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 批量测试异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试hotmail账户修复效果")
    print("🔧 修复内容: hotmail.com账户现在使用consumers端点")
    print()
    
    test_hotmail_accounts()
    test_single_hotmail_account()
    test_batch_hotmail_processing()
    
    print("\n" + "=" * 60)
    print("✅ hotmail账户修复测试完成")
    print()
    print("📋 修复说明:")
    print("   - hotmail.com账户现在使用正确的consumers OAuth2端点")
    print("   - outlook.com账户继续使用common端点")
    print("   - 系统会自动根据域名选择正确的端点")
    print("   - 如果仍有失败，可能是refresh_token本身无效")
