name: Deploy Application

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Run backend tests
      run: |
        cd backend
        python -m pytest tests/ -v || true

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend-vue/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend-vue
        npm ci
    
    - name: Build frontend
      run: |
        cd frontend-vue
        npm run build
    
    - name: Test frontend build
      run: |
        cd frontend-vue
        ls -la dist/

  deploy-notification:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deployment notification
      run: |
        echo "✅ Tests passed! Ready for deployment:"
        echo "📱 Frontend: Will be auto-deployed by Netlify"
        echo "🖥️ Backend: Manual deployment to server required"
