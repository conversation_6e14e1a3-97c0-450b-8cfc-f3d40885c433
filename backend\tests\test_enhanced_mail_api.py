"""
增强版邮件获取 API 测试脚本 - 测试多文件夹功能
"""

import requests
import json
import time

# API 基础 URL
BASE_URL = "http://localhost:8000"

def test_enhanced_mail_api():
    """测试增强版邮件获取 API (多文件夹功能)"""
    print("开始测试增强版邮件获取 API...")
    print(f"API 服务地址: {BASE_URL}")
    print("=" * 80)
    
    # 获取所有账户
    print("1. 获取所有可用账户...")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts", timeout=10)
        if response.status_code == 200:
            accounts = response.json()
            print(f"   找到 {len(accounts)} 个账户")
            
            # 过滤掉测试账户
            real_accounts = [acc for acc in accounts if not acc['email_address'].startswith('invalid')]
            print(f"   其中 {len(real_accounts)} 个真实账户")
            
            if not real_accounts:
                print("   没有真实账户可供测试")
                return
                
        else:
            print(f"   获取账户失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    # 选择测试账户
    test_account = real_accounts[0]
    test_email = test_account['email_address']
    print(f"\n使用测试账户: {test_email}")
    
    print("\n" + "=" * 80)
    print("2. 测试向后兼容性 - 单文件夹模式")
    
    # 测试原有的单文件夹接口
    print("\n   2.1 测试原有的 folder 参数...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folder": "inbox", "limit": 2},
            timeout=60
        )
        
        print(f"      状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"      ✓ 成功获取 {result['total_count']} 封邮件")
            print(f"      ✓ 返回的文件夹列表: {result['folders']}")
            
            # 验证每封邮件都有 folder 字段
            for i, msg in enumerate(result['messages'][:2], 1):
                if 'folder' in msg:
                    print(f"         邮件 {i}: folder='{msg['folder']}', 主题='{msg['subject'][:30]}...'")
                else:
                    print(f"         ✗ 邮件 {i} 缺少 folder 字段")
        else:
            result = response.json()
            print(f"      ✗ 失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"      ✗ 请求失败: {e}")
    
    print("\n" + "=" * 80)
    print("3. 测试新功能 - 多文件夹模式")
    
    # 测试新的多文件夹接口
    print("\n   3.1 测试 folders 参数 (多文件夹)...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": "inbox,junk", "limit": 3},
            timeout=60
        )
        
        print(f"      状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"      ✓ 成功获取 {result['total_count']} 封邮件")
            print(f"      ✓ 请求的文件夹: ['inbox', 'junk']")
            print(f"      ✓ 返回的文件夹列表: {result['folders']}")
            
            # 统计各文件夹的邮件数量
            folder_counts = {}
            for msg in result['messages']:
                folder = msg.get('folder', 'unknown')
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            print(f"      ✓ 邮件分布: {folder_counts}")
            
            # 验证邮件按时间排序
            dates = [msg.get('date', '') for msg in result['messages']]
            print(f"      ✓ 邮件时间范围: {dates[0] if dates else 'N/A'} ~ {dates[-1] if dates else 'N/A'}")
            
        else:
            result = response.json()
            print(f"      ✗ 失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"      ✗ 请求失败: {e}")
    
    print("\n   3.2 测试 POST 聚合接口...")
    try:
        aggregate_request = {
            "email_address": test_email,
            "folders": ["inbox", "junk", "sent"],
            "limit": 2
        }
        
        response = requests.post(
            f"{BASE_URL}/api/mail/aggregate",
            json=aggregate_request,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        print(f"      状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"      ✓ 聚合成功获取 {result['total_count']} 封邮件")
            print(f"      ✓ 请求的文件夹: {aggregate_request['folders']}")
            print(f"      ✓ 返回的文件夹列表: {result['folders']}")
            
            # 统计各文件夹的邮件数量
            folder_counts = {}
            for msg in result['messages']:
                folder = msg.get('folder', 'unknown')
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            print(f"      ✓ 邮件分布: {folder_counts}")
            
        else:
            result = response.json()
            print(f"      ✗ 聚合失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"      ✗ 聚合请求失败: {e}")
    
    print("\n" + "=" * 80)
    print("4. 测试错误处理")
    
    # 测试无效文件夹
    print("\n   4.1 测试无效文件夹...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": "inbox,invalid_folder,junk", "limit": 2},
            timeout=30
        )
        
        print(f"      状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"      ✓ 部分成功获取 {result['total_count']} 封邮件")
            print(f"      ✓ 请求的文件夹: ['inbox', 'invalid_folder', 'junk']")
            print(f"      ✓ 实际处理的文件夹: {result['folders']}")
            
            # 检查是否有来自有效文件夹的邮件
            folder_counts = {}
            for msg in result['messages']:
                folder = msg.get('folder', 'unknown')
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            print(f"      ✓ 邮件分布: {folder_counts}")
            
        else:
            result = response.json()
            print(f"      ⚠ 响应: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"      ✗ 请求失败: {e}")
    
    # 测试参数验证
    print("\n   4.2 测试参数验证...")
    
    # 测试无效的 limit
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": "inbox", "limit": 100},  # 超过最大限制
            timeout=10
        )
        
        print(f"      无效 limit 测试 - 状态码: {response.status_code}")
        if response.status_code == 400:
            result = response.json()
            print(f"      ✓ 正确拒绝无效参数: {result['message']}")
        else:
            print(f"      ⚠ 未正确验证参数")
            
    except Exception as e:
        print(f"      ✗ 参数验证测试失败: {e}")
    
    # 测试空文件夹列表
    try:
        aggregate_request = {
            "email_address": test_email,
            "folders": [],  # 空列表
            "limit": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/api/mail/aggregate",
            json=aggregate_request,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"      空文件夹列表测试 - 状态码: {response.status_code}")
        if response.status_code == 400:
            result = response.json()
            print(f"      ✓ 正确拒绝空文件夹列表: {result['message']}")
        else:
            print(f"      ⚠ 未正确验证空文件夹列表")
            
    except Exception as e:
        print(f"      ✗ 空文件夹列表测试失败: {e}")
    
    print("\n" + "=" * 80)
    print("5. 性能测试")
    
    # 测试多文件夹性能
    print("\n   5.1 测试多文件夹性能...")
    try:
        start_time = time.time()
        
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": "inbox,junk,sent,drafts", "limit": 5},
            timeout=120
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"      状态码: {response.status_code}")
        print(f"      响应时间: {duration:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            print(f"      ✓ 获取 {result['total_count']} 封邮件")
            print(f"      ✓ 平均每封邮件: {duration/max(result['total_count'], 1):.2f} 秒")
            
        else:
            result = response.json()
            print(f"      ⚠ 性能测试失败: {result.get('message', '未知错误')}")
            
    except Exception as e:
        print(f"      ✗ 性能测试失败: {e}")
    
    print("\n" + "=" * 80)
    print("✓ 增强版邮件获取 API 测试完成！")


if __name__ == "__main__":
    test_enhanced_mail_api()
