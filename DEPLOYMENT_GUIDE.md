# 🚀 前后端分离部署指南

## 📊 部署架构

```
┌─────────────────┐    HTTPS     ┌─────────────────┐
│   Netlify CDN   │ ──────────► │  自有服务器      │
│   (前端Vue.js)   │             │  (后端Flask)    │
│                 │             │  Port: 8000     │
└─────────────────┘             └─────────────────┘
```

## 🎯 部署优势

- ✅ **前端**: 免费CDN、自动HTTPS、全球加速
- ✅ **后端**: 完全控制、数据安全、灵活配置
- ✅ **成本**: 前端免费，后端按需付费
- ✅ **扩展**: 前端自动扩展，后端可升级配置

## 📋 部署步骤

### 第一步: 后端服务器部署

#### 选项1: 传统部署
```bash
# 1. 上传代码到服务器
scp -r backend/ user@your-server:/opt/

# 2. 运行部署脚本
chmod +x backend/deploy_server.sh
sudo ./backend/deploy_server.sh

# 3. 配置环境变量
sudo systemctl edit mail-aggregator
# 添加:
# [Service]
# Environment=FRONTEND_URL=https://your-app.netlify.app
```

#### 选项2: Docker部署
```bash
# 1. 构建镜像
cd backend
docker build -t mail-aggregator .

# 2. 运行容器
docker-compose up -d

# 3. 检查状态
docker-compose ps
docker-compose logs -f
```

### 第二步: 前端Netlify部署

#### 方法1: Git自动部署（推荐）
1. 将代码推送到GitHub
2. 在Netlify中连接仓库
3. 配置构建设置：
   - Base directory: `frontend-vue`
   - Build command: `npm run build`
   - Publish directory: `frontend-vue/dist`

#### 方法2: 手动部署
```bash
# 1. 配置API地址
echo "VITE_API_BASE_URL=https://your-server.com:8000" > frontend-vue/.env.production

# 2. 构建项目
cd frontend-vue
npm install
npm run build

# 3. 上传dist文件夹到Netlify
```

### 第三步: 配置连接

#### 1. 更新后端CORS
在服务器上编辑配置：
```bash
# 设置前端域名
export FRONTEND_URL="https://your-app.netlify.app"
sudo systemctl restart mail-aggregator
```

#### 2. 更新前端API地址
在Netlify环境变量中设置：
- Variable: `VITE_API_BASE_URL`
- Value: `https://your-server.com:8000`

## 🔧 配置详情

### 后端服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+
- **Python**: 3.8+
- **内存**: 1GB+
- **存储**: 10GB+
- **网络**: 公网IP，开放8000端口

### 域名和SSL
- **后端**: 可选配置域名和SSL证书
- **前端**: Netlify自动提供HTTPS

### 环境变量配置

#### 后端环境变量
```bash
FLASK_ENV=production
DATABASE_PATH=/app/data/accounts.db
LOG_FILE=/app/logs/app.log
FRONTEND_URL=https://your-app.netlify.app
LOG_LEVEL=INFO
PORT=8000
```

#### 前端环境变量
```bash
VITE_API_BASE_URL=https://your-server.com:8000
```

## 🚨 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8000/tcp  # API
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

### SSL证书（可选）
```bash
# 使用Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

## 📊 监控和维护

### 服务状态检查
```bash
# 检查服务状态
sudo systemctl status mail-aggregator

# 查看日志
sudo journalctl -u mail-aggregator -f

# 重启服务
sudo systemctl restart mail-aggregator
```

### 性能监控
```bash
# 检查资源使用
htop
df -h
free -h

# 检查网络连接
netstat -tlnp | grep 8000
```

## 🔄 更新部署

### 后端更新
```bash
# 1. 停止服务
sudo systemctl stop mail-aggregator

# 2. 更新代码
cd /opt/mail-aggregator
git pull  # 或重新上传文件

# 3. 重启服务
sudo systemctl start mail-aggregator
```

### 前端更新
- Git部署: 推送代码到仓库，Netlify自动构建
- 手动部署: 重新构建并上传dist文件夹

## 📋 部署检查清单

### 后端部署验证
- [ ] 服务器可访问
- [ ] Python环境正确
- [ ] 依赖安装完成
- [ ] 服务正常启动
- [ ] 端口8000开放
- [ ] API响应正常
- [ ] 数据库初始化
- [ ] 日志正常记录

### 前端部署验证
- [ ] Netlify构建成功
- [ ] 网站可访问
- [ ] API连接正常
- [ ] 功能测试通过
- [ ] 跨域请求正常
- [ ] 移动端适配
- [ ] 性能测试通过

### 集成测试
- [ ] 账户管理功能
- [ ] 邮件获取功能
- [ ] 导入导出功能
- [ ] 错误处理正常
- [ ] 性能表现良好

## 🎉 部署完成

部署完成后，您将拥有：
- 🌐 **前端**: https://your-app.netlify.app
- 🔧 **后端**: https://your-server.com:8000
- 📊 **监控**: 完整的日志和状态监控
- 🔒 **安全**: HTTPS加密和防火墙保护
