"""
性能测试脚本 - 对比同步和异步邮件获取的性能差异
"""

import time
import requests
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_BASE_URL = "http://localhost:8000"

def test_sync_vs_async():
    """对比同步和异步邮件获取的性能"""
    
    # 测试数据
    test_data = {
        "accounts": [],  # 空数组表示获取所有账户
        "folders": ["inbox"],
        "limit": 10
    }
    
    print("=" * 60)
    print("📊 邮件获取性能对比测试")
    print("=" * 60)
    
    # 测试同步版本
    print("\n🔄 测试同步版本 (/api/mail/aggregate)")
    sync_start = time.time()
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/mail/aggregate",
            json=test_data,
            timeout=120
        )
        sync_end = time.time()
        sync_duration = sync_end - sync_start
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 同步获取成功")
            print(f"   耗时: {sync_duration:.2f} 秒")
            print(f"   账户数: {len(data.get('successful_accounts', []))}")
            print(f"   邮件数: {data.get('total_count', 0)}")
        else:
            print(f"❌ 同步获取失败: {response.status_code}")
            print(f"   错误: {response.text}")
            sync_duration = None
            
    except Exception as e:
        print(f"❌ 同步获取异常: {e}")
        sync_duration = None
    
    # 等待一秒
    time.sleep(1)
    
    # 测试异步版本
    print("\n⚡ 测试异步版本 (/api/mail/aggregate-async)")
    async_start = time.time()
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/mail/aggregate-async",
            json=test_data,
            timeout=120
        )
        async_end = time.time()
        async_duration = async_end - async_start
        
        if response.status_code == 200:
            data = response.json()
            performance = data.get('performance', {})
            print(f"✅ 异步获取成功")
            print(f"   耗时: {async_duration:.2f} 秒")
            print(f"   后端处理耗时: {performance.get('duration', 0):.2f} 秒")
            print(f"   账户数: {len(data.get('successful_accounts', []))}")
            print(f"   邮件数: {data.get('total_count', 0)}")
            print(f"   并发处理: {performance.get('concurrent_processing', False)}")
        else:
            print(f"❌ 异步获取失败: {response.status_code}")
            print(f"   错误: {response.text}")
            async_duration = None
            
    except Exception as e:
        print(f"❌ 异步获取异常: {e}")
        async_duration = None
    
    # 性能对比
    print("\n" + "=" * 60)
    print("📈 性能对比结果")
    print("=" * 60)
    
    if sync_duration and async_duration:
        improvement = ((sync_duration - async_duration) / sync_duration) * 100
        speedup = sync_duration / async_duration
        
        print(f"同步版本耗时: {sync_duration:.2f} 秒")
        print(f"异步版本耗时: {async_duration:.2f} 秒")
        print(f"性能提升: {improvement:.1f}%")
        print(f"速度倍数: {speedup:.2f}x")
        
        if improvement > 0:
            print(f"🚀 异步版本更快 {improvement:.1f}%")
        else:
            print(f"⚠️ 同步版本更快 {abs(improvement):.1f}%")
    else:
        print("⚠️ 无法进行性能对比，某个版本测试失败")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_sync_vs_async()