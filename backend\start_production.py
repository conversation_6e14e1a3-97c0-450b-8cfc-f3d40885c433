"""
生产环境启动脚本
"""

import os
import logging
from flask import Flask
from flask_cors import CORS
from production_config import ProductionConfig

# 导入应用模块
from database import init_db
from flask_main import app

def setup_production_app():
    """配置生产环境应用"""
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, ProductionConfig.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(ProductionConfig.LOG_FILE),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("启动生产环境应用...")
    
    # 更新CORS配置
    cors_origins = ProductionConfig.get_cors_origins()
    logger.info(f"CORS允许的源: {cors_origins}")
    
    # 重新配置CORS
    CORS(app, 
         origins=cors_origins,
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Content-Type", "Authorization"],
         supports_credentials=True)
    
    # 确保数据库目录存在
    db_dir = os.path.dirname(ProductionConfig.DATABASE_PATH)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"创建数据库目录: {db_dir}")
    
    # 初始化数据库
    try:
        init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise
    
    return app

if __name__ == "__main__":
    # 设置生产环境
    os.environ['FLASK_ENV'] = 'production'
    
    # 配置应用
    app = setup_production_app()
    
    # 启动应用
    app.run(
        host=ProductionConfig.HOST,
        port=ProductionConfig.PORT,
        debug=ProductionConfig.DEBUG
    )
