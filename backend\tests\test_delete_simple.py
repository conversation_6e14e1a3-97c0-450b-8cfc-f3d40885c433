"""
简单测试删除功能
"""

import requests
import json

# API 基础 URL
API_BASE_URL = "http://localhost:8000"

def main():
    print("🧪 简单删除功能测试")
    print("=" * 40)
    
    # 1. 获取当前账户列表
    print("1. 获取当前账户列表...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            print(f"   当前有 {len(accounts)} 个账户")
            
            if len(accounts) > 0:
                # 显示前几个账户
                for i, account in enumerate(accounts[:3]):
                    print(f"   {i+1}. {account['email_address']} (ID: {account['id']})")
            else:
                print("   没有账户，先添加一个测试账户...")
                
                # 添加测试账户
                test_account = {
                    "email_address": "<EMAIL>",
                    "client_id": "delete123-4567-89ab-cdef-123456789abc",
                    "refresh_token": "M.C519_BAY.0.U.-DeleteTestToken123456789abcdef"
                }
                
                add_response = requests.post(
                    f"{API_BASE_URL}/api/accounts",
                    json=test_account,
                    headers={"Content-Type": "application/json"}
                )
                
                if add_response.status_code == 201:
                    account_data = add_response.json()
                    accounts = [account_data]
                    print(f"   ✅ 测试账户已添加: {account_data['email_address']} (ID: {account_data['id']})")
                else:
                    print(f"   ❌ 添加测试账户失败: {add_response.status_code}")
                    return
        else:
            print(f"   ❌ 获取账户列表失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return
    
    # 2. 测试删除第一个账户
    if len(accounts) > 0:
        target_account = accounts[0]
        account_id = target_account['id']
        account_email = target_account['email_address']
        
        print(f"\n2. 测试删除账户: {account_email} (ID: {account_id})")
        
        try:
            # 调用删除API（使用邮箱地址）
            delete_response = requests.delete(f"{API_BASE_URL}/api/accounts/{account_email}")

            print(f"   删除请求状态码: {delete_response.status_code}")
            print(f"   删除请求响应: {delete_response.text}")

            if delete_response.status_code == 200:
                print("   ✅ 删除请求成功")
                
                # 验证删除结果
                print("   验证删除结果...")
                verify_response = requests.get(f"{API_BASE_URL}/api/accounts")
                
                if verify_response.status_code == 200:
                    updated_accounts = verify_response.json()
                    deleted_account = next((acc for acc in updated_accounts if acc['id'] == account_id), None)
                    
                    if deleted_account is None:
                        print("   ✅ 账户已成功删除")
                        print(f"   账户数量从 {len(accounts)} 减少到 {len(updated_accounts)}")
                    else:
                        print("   ❌ 账户仍然存在，删除失败")
                else:
                    print("   ❌ 无法验证删除结果")
            else:
                print(f"   ❌ 删除请求失败: {delete_response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 删除测试失败: {e}")
    
    # 3. 测试删除不存在的账户
    print(f"\n3. 测试删除不存在的账户...")
    try:
        fake_id = "999999"
        delete_response = requests.delete(f"{API_BASE_URL}/api/accounts/{fake_id}")
        
        print(f"   删除不存在账户的状态码: {delete_response.status_code}")
        
        if delete_response.status_code == 404:
            print("   ✅ 正确返回404错误")
        else:
            print(f"   ⚠️ 预期404，实际返回: {delete_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 删除功能测试完成")

if __name__ == "__main__":
    main()
