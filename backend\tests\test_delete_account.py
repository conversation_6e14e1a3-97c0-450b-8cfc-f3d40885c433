"""
测试账户删除功能
"""

import requests
import json
import time

# API 基础 URL
API_BASE_URL = "http://localhost:8000"

def test_backend_delete_api():
    """测试后端删除API"""
    print("🔧 测试后端删除API...")
    
    # 先添加一个测试账户
    test_account = {
        "email_address": "<EMAIL>",
        "client_id": "delete123-4567-89ab-cdef-123456789abc",
        "refresh_token": "M.C519_BAY.0.U.-DeleteTestToken123456789abcdef"
    }
    
    try:
        # 添加测试账户
        print("   1. 添加测试账户...")
        add_response = requests.post(
            f"{API_BASE_URL}/api/accounts",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        
        if add_response.status_code == 201:
            account_data = add_response.json()
            account_id = account_data['id']
            print(f"   ✅ 测试账户已添加，ID: {account_id}")
        elif add_response.status_code == 409:
            # 账户已存在，获取ID
            print("   ℹ️ 测试账户已存在，获取ID...")
            accounts_response = requests.get(f"{API_BASE_URL}/api/accounts")
            if accounts_response.status_code == 200:
                accounts = accounts_response.json()
                test_account_data = next((acc for acc in accounts if acc['email_address'] == test_account['email_address']), None)
                if test_account_data:
                    account_id = test_account_data['id']
                    print(f"   ✅ 找到测试账户，ID: {account_id}")
                else:
                    print("   ❌ 无法找到测试账户")
                    return False
            else:
                print("   ❌ 无法获取账户列表")
                return False
        else:
            print(f"   ❌ 添加测试账户失败: {add_response.status_code}")
            return False
        
        # 测试删除API
        print("   2. 测试删除API...")
        delete_response = requests.delete(f"{API_BASE_URL}/api/accounts/{account_id}")
        
        if delete_response.status_code == 200:
            print("   ✅ 删除API调用成功")
            
            # 验证账户是否真的被删除
            print("   3. 验证账户是否被删除...")
            verify_response = requests.get(f"{API_BASE_URL}/api/accounts")
            if verify_response.status_code == 200:
                accounts = verify_response.json()
                deleted_account = next((acc for acc in accounts if acc['id'] == account_id), None)
                
                if deleted_account is None:
                    print("   ✅ 账户已成功删除")
                    return True
                else:
                    print("   ❌ 账户仍然存在，删除失败")
                    return False
            else:
                print("   ❌ 无法验证删除结果")
                return False
        else:
            print(f"   ❌ 删除API调用失败: {delete_response.status_code}")
            print(f"   错误信息: {delete_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_frontend_delete_ui():
    """测试前端删除UI"""
    print("\n🎨 测试前端删除UI...")
    
    try:
        # 检查前端页面
        response = requests.get("http://localhost:3000", timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含删除按钮相关的CSS类
            if 'delete-account-btn' in content:
                print("   ✅ 删除按钮CSS类存在")
            else:
                print("   ⚠️ 删除按钮CSS类可能不存在")
            
            # 检查是否包含删除相关的JavaScript函数
            if 'handleDeleteAccountClick' in content:
                print("   ✅ 删除处理函数存在")
            else:
                print("   ⚠️ 删除处理函数可能不存在")
            
            # 检查是否包含删除API函数
            if 'deleteAccount' in content:
                print("   ✅ 删除API函数存在")
            else:
                print("   ⚠️ 删除API函数可能不存在")
            
            return True
        else:
            print(f"   ❌ 前端页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_account_management_workflow():
    """测试完整的账户管理工作流程"""
    print("\n🔄 测试账户管理工作流程...")
    
    workflow_account = {
        "email_address": "<EMAIL>",
        "client_id": "workflow1-2345-6789-abcd-ef1234567890",
        "refresh_token": "M.C520_BAY.0.U.-WorkflowDeleteToken123456789abcdef"
    }
    
    try:
        # 1. 添加账户
        print("   1. 添加测试账户...")
        add_response = requests.post(
            f"{API_BASE_URL}/api/accounts",
            json=workflow_account,
            headers={"Content-Type": "application/json"}
        )
        
        if add_response.status_code in [201, 409]:
            if add_response.status_code == 201:
                account_data = add_response.json()
                account_id = account_data['id']
                print(f"   ✅ 账户添加成功，ID: {account_id}")
            else:
                # 获取现有账户ID
                accounts_response = requests.get(f"{API_BASE_URL}/api/accounts")
                accounts = accounts_response.json()
                existing_account = next((acc for acc in accounts if acc['email_address'] == workflow_account['email_address']), None)
                account_id = existing_account['id']
                print(f"   ✅ 使用现有账户，ID: {account_id}")
        else:
            print(f"   ❌ 添加账户失败: {add_response.status_code}")
            return False
        
        # 2. 验证账户存在
        print("   2. 验证账户存在...")
        accounts_response = requests.get(f"{API_BASE_URL}/api/accounts")
        if accounts_response.status_code == 200:
            accounts = accounts_response.json()
            account_exists = any(acc['id'] == account_id for acc in accounts)
            
            if account_exists:
                print("   ✅ 账户存在确认")
            else:
                print("   ❌ 账户不存在")
                return False
        else:
            print("   ❌ 无法获取账户列表")
            return False
        
        # 3. 删除账户
        print("   3. 删除账户...")
        delete_response = requests.delete(f"{API_BASE_URL}/api/accounts/{account_id}")
        
        if delete_response.status_code == 200:
            print("   ✅ 删除请求成功")
        else:
            print(f"   ❌ 删除请求失败: {delete_response.status_code}")
            return False
        
        # 4. 验证账户已删除
        print("   4. 验证账户已删除...")
        time.sleep(1)  # 等待删除操作完成
        
        verify_response = requests.get(f"{API_BASE_URL}/api/accounts")
        if verify_response.status_code == 200:
            accounts = verify_response.json()
            account_still_exists = any(acc['id'] == account_id for acc in accounts)
            
            if not account_still_exists:
                print("   ✅ 账户已成功删除")
                return True
            else:
                print("   ❌ 账户仍然存在")
                return False
        else:
            print("   ❌ 无法验证删除结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 工作流程测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 账户删除功能测试")
    print("=" * 60)
    
    # 检查服务状态
    print("检查服务状态...")
    try:
        backend_response = requests.get(f"{API_BASE_URL}/api/accounts", timeout=5)
        frontend_response = requests.get("http://localhost:3000", timeout=5)
        
        if backend_response.status_code == 200:
            print("   ✅ 后端服务正常")
        else:
            print(f"   ❌ 后端服务异常: {backend_response.status_code}")
            return
        
        if frontend_response.status_code == 200:
            print("   ✅ 前端服务正常")
        else:
            print(f"   ❌ 前端服务异常: {frontend_response.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ 服务检查失败: {e}")
        return
    
    # 执行测试
    tests = [
        ("后端删除API", test_backend_delete_api),
        ("前端删除UI", test_frontend_delete_ui),
        ("账户管理工作流程", test_account_management_workflow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results[test_name] = False
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 账户删除功能测试通过")
        print("\n📖 使用指南:")
        print("   1. 访问前端: http://localhost:3000")
        print("   2. 在左侧账户列表中，鼠标悬停在账户上")
        print("   3. 点击出现的红色 '✕' 删除按钮")
        print("   4. 确认删除操作")
        print("   5. 账户将从列表中移除")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
