"""
批量添加真实邮箱账户到数据库
"""

import requests
import json

# API 基础 URL
BASE_URL = "http://localhost:8000"

# 真实账户数据
real_accounts = [
    {
        "email_address": "<EMAIL>",
        "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
        "refresh_token": "M.C519_BAY.0.U.-Ci6kMqO!ZLPI8gdNqc41uknWXLOd7*PC*sXAfW02ioLtQ!nGAC*3MwndY8wwl!6mS2nOJg0gsgd5kVh3KHX1nWHXNepIEXCxdbh04sQILosGrmaMI8nKr3wDXlSHP5UiACTqCfoyosj5lVHBLmPZz8nZAVrGCsMhoIX0I8NXgZXD6Sm0erk5Oz2oG08XeyvjLz8WM*YUF93A!Dv7*!Y3MSExc04d1G52oTbjihW8WxDYY1d!22GNyO3JPk4hQnqp3DcfK9z6vmVr3Ro54UoWrWXfsSWbi3OOggjGoWjpoELZ0Fo3ob0fNJAI2PETzZHwaH7r7sktXXZ3wAmh3mFWl7z4OdvSWYFSuzmluLnP6dFYyZrSCku2Riz*d1ZliRHz0g$$"
    },
    {
        "email_address": "<EMAIL>",
        "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
        "refresh_token": "M.C514_BL2.0.U.-CvXmR0KiLwydIfm!Wfb16SRO*gOBecIIKioTbPnuLHLrwTrmXsxxV8SeegFwPzmP6xXVJn2OxQ1vz4KMu!R*sBFgtxXXxVc2TTuA66KSMihNeG*5PQNqmH2y9bzDefaajTCUBmQQv1AHcRc674iJB0435aTnlWjvXKBqjOEYXcIm!AVriCkTFVwBk2QRIpMHoEXx*Aw9r*shIH5Xr7VKm10LENQ4IhzdwLX2SuA9TK1a*LJL3TgEIOf8pqgDjkL8aeQiEEBCKMm9BjtaSZOUZbMBY39axPFh19GcRlYH8eOlU28UpYUuq31GMUWu176TjaSg6V6tawJ0c*jAjGkvWrn*KyCcOyXkqqKpuAF6MamKeOmDA6fBryJK9*5izUsvqQ$$"
    },
    {
        "email_address": "<EMAIL>",
        "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
        "refresh_token": "M.C534_SN1.0.U.-CoGwqgK4alfp!zfFT!wh5DZfXILQB7ANvY5!eYyApHwIZc0YZdAQ!bJqslTvANjD7II0l7PFFx3GztEQuUPJ7vpqWcyr*6viAD7gWSCtYXWZU6GVkpcNNmJM7vfNDiNAWlO9EzF0YWO74gvznUYucLVBeysQGHMCYj*knOWE6oFXw2X3j2aDh!PQ9P6My4qrVtf1C0ZpiX4pGUTEzzdag0*R0OBTETzD0XWvxLuCrl4zf1gxuzpbhIb8Bqol3vVp4w8m78vxgxrleI!1FIjyLiGtGXV!LRReisyQ1SpitjnGPVb9erdXLJjaDcLiyzRzZ8OSwO91s7rlu9tNgcwYAlaT3Ua6o0fwgXkEKzqe6Vp0NyKFGYAKnqT2NR94evwq8A$$"
    },
    {
        "email_address": "<EMAIL>",
        "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
        "refresh_token": "M.C518_BAY.0.U.-CpdyiK9QmPKggIv9h*f9bGW04TmWsagBOtOJUnmdsRcErtG5ZV52LrEiLA44U!5xE1BgFsXu1V!H1r8mII3sTDO20zXH3zhwGSs1oTJzHHmVXbGsPJmXYW0DU7tz1RAfg36wQ1Led37dMFWFogL*vqRwJZ1sVdKxn!82ZX2ejl3Su36OjOCWUvL!ff4Q96U57*4d9Rz2pb9d!zgyYl!1G!YOp1z9a07XHc2PUGLOgdyREiplQcSatcHbkNsuPnEPbXuaMrTVS0UvavBccEjDAS7!KolWDDtYlwmt0DVbNYwtjmy0589Y8DUbYHMKKAll6TI9t!Ft*AGtR!edtbcsZUjqFf2Beb355Znmzv5EQD970HJ8j*bfooZJMR71PtePNg$$"
    },
    {
        "email_address": "<EMAIL>",
        "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
        "refresh_token": "M.C531_SN1.0.U.-CqCwAiL1*knKTa0vDh8I6nxtjrhHSgmLA9Ap*G!20WetQ*cMqKBjaWDasg1pFE1lby6H8RFF3rF4vV8dRPaE8xCox0iur*lc3Tjm98AZoEBE5UiO9L*Ql3*GQjcFiUsVqJQjpwOf29DWnkZRsUu6lxZ7vLYFQuDjPi78L!VV9T!mgdJhHtlbJjILxRcxJO5vQQYbufduielKUc4My89howTpNuuA03eutQud!f1Bjjk7fhwN7wpEYYQrfLQf*ms9bULCuR2yck8!D!RK38o2aPaFDN6mrTf5oBlubqzsSCu78y9s8397ZlsR9kOdmQ42YLlA4iUvhtJZ7sNE7782MzoodRTdMzlXOP!0yOlurTF8UxjhNvgeVO20TVwj7EGF*f7K*I5kZ2Ld!IzJWbwDmoc$"
    }
]

def add_accounts():
    """批量添加账户"""
    print("开始批量添加真实邮箱账户...")
    print(f"API 服务地址: {BASE_URL}")
    print("=" * 60)
    
    success_count = 0
    error_count = 0
    
    for i, account in enumerate(real_accounts, 1):
        print(f"\n{i}. 添加账户: {account['email_address']}")
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/accounts",
                json=account,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 201:
                print(f"   ✓ 成功添加")
                success_count += 1
            elif response.status_code == 409:
                print(f"   ⚠ 账户已存在")
                success_count += 1  # 已存在也算成功
            else:
                print(f"   ✗ 失败 - 状态码: {response.status_code}")
                print(f"   响应: {response.json()}")
                error_count += 1
                
        except requests.exceptions.ConnectionError:
            print(f"   ✗ 连接失败 - 请确保 API 服务器正在运行")
            error_count += 1
        except Exception as e:
            print(f"   ✗ 错误: {e}")
            error_count += 1
    
    print("\n" + "=" * 60)
    print(f"批量添加完成！")
    print(f"成功: {success_count} 个账户")
    print(f"失败: {error_count} 个账户")
    
    # 显示所有账户
    print("\n获取所有账户列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts", timeout=10)
        if response.status_code == 200:
            accounts = response.json()
            print(f"数据库中共有 {len(accounts)} 个账户:")
            for account in accounts:
                print(f"  - {account['email_address']} (状态: {account['status']})")
        else:
            print(f"获取账户列表失败: {response.status_code}")
    except Exception as e:
        print(f"获取账户列表时出错: {e}")


if __name__ == "__main__":
    add_accounts()
