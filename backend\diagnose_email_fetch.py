"""
诊断邮件获取功能问题
"""

import requests
import json
import time
from datetime import datetime

# API 基础 URL
API_BASE_URL = "http://localhost:8000"

def test_backend_apis():
    """测试后端API"""
    print("🔧 测试后端API")
    print("=" * 50)
    
    # 1. 测试账户列表API
    print("1. 测试账户列表API...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts", timeout=10)
        if response.status_code == 200:
            accounts = response.json()
            print(f"   ✅ 账户列表API正常，共 {len(accounts)} 个账户")
            
            if len(accounts) > 0:
                # 显示前几个账户
                for i, account in enumerate(accounts[:3], 1):
                    print(f"   {i}. {account['email_address']} (状态: {account['status']})")
                return accounts
            else:
                print("   ⚠️ 没有账户数据")
                return []
        else:
            print(f"   ❌ 账户列表API失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ 账户列表API异常: {e}")
        return []

def test_single_email_fetch(accounts):
    """测试单个账户邮件获取"""
    print("\n2. 测试单个账户邮件获取...")
    
    if not accounts:
        print("   ⚠️ 没有账户可测试")
        return False
    
    test_account = accounts[0]
    email = test_account['email_address']
    
    print(f"   🎯 测试账户: {email}")
    
    # 测试不同文件夹
    folders_to_test = ['inbox', 'sent', 'drafts']
    
    for folder in folders_to_test:
        print(f"   📁 测试文件夹: {folder}")
        
        try:
            url = f"{API_BASE_URL}/api/mail/{email}?folders={folder}&limit=5"
            print(f"      请求URL: {url}")
            
            start_time = time.time()
            response = requests.get(url, timeout=30)
            end_time = time.time()
            
            print(f"      响应时间: {end_time - start_time:.2f}秒")
            print(f"      状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get('messages', [])
                print(f"      ✅ 成功获取 {len(messages)} 封邮件")
                
                if messages:
                    # 显示第一封邮件的基本信息
                    first_email = messages[0]
                    print(f"      📧 第一封邮件:")
                    print(f"         主题: {first_email.get('subject', 'N/A')[:50]}...")
                    print(f"         发件人: {first_email.get('from', 'N/A')}")
                    print(f"         时间: {first_email.get('date', 'N/A')}")
                
                return True
            else:
                print(f"      ❌ 请求失败: {response.status_code}")
                print(f"      错误信息: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"      ❌ 请求超时 (30秒)")
        except Exception as e:
            print(f"      ❌ 请求异常: {e}")
    
    return False

def test_aggregated_email_fetch(accounts):
    """测试聚合邮件获取"""
    print("\n3. 测试聚合邮件获取...")
    
    if len(accounts) < 2:
        print("   ⚠️ 账户数量不足，跳过聚合测试")
        return False
    
    # 选择前两个账户进行测试
    test_accounts = [acc['email_address'] for acc in accounts[:2]]
    
    print(f"   🎯 测试账户: {test_accounts}")
    
    try:
        payload = {
            "accounts": test_accounts,
            "folders": ["inbox"],
            "limit": 5
        }
        
        print(f"   📤 请求数据: {json.dumps(payload, indent=2)}")
        
        start_time = time.time()
        response = requests.post(
            f"{API_BASE_URL}/api/mail/aggregate",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        end_time = time.time()
        
        print(f"   ⏱️ 响应时间: {end_time - start_time:.2f}秒")
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            messages = data.get('messages', [])
            total_count = data.get('total_count', 0)
            
            print(f"   ✅ 聚合成功，共 {len(messages)} 封邮件 (总计: {total_count})")
            
            # 按账户统计
            account_stats = {}
            for msg in messages:
                account = msg.get('account', 'unknown')
                account_stats[account] = account_stats.get(account, 0) + 1
            
            print("   📈 账户统计:")
            for account, count in account_stats.items():
                print(f"      {account}: {count} 封")
            
            return True
        else:
            print(f"   ❌ 聚合失败: {response.status_code}")
            print(f"   错误信息: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print(f"   ❌ 聚合请求超时 (60秒)")
    except Exception as e:
        print(f"   ❌ 聚合请求异常: {e}")
    
    return False

def test_token_validity(accounts):
    """测试refresh_token有效性"""
    print("\n4. 测试refresh_token有效性...")
    
    if not accounts:
        print("   ⚠️ 没有账户可测试")
        return
    
    for i, account in enumerate(accounts[:3], 1):
        email = account['email_address']
        print(f"   {i}. 测试账户: {email}")
        
        try:
            # 尝试获取邮件来验证token
            response = requests.get(
                f"{API_BASE_URL}/api/mail/{email}?folders=inbox&limit=1",
                timeout=20
            )
            
            if response.status_code == 200:
                print(f"      ✅ Token有效")
            elif response.status_code == 401:
                print(f"      ❌ Token无效或已过期")
            elif response.status_code == 403:
                print(f"      ❌ Token权限不足")
            else:
                print(f"      ⚠️ 未知状态: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ 测试异常: {e}")

def test_frontend_integration():
    """测试前端集成"""
    print("\n5. 测试前端集成...")
    
    try:
        # 检查前端服务
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端服务正常")
            
            # 检查JavaScript文件
            js_response = requests.get("http://localhost:3000/script.js", timeout=5)
            if js_response.status_code == 200:
                js_content = js_response.text
                
                # 检查关键函数
                functions_to_check = [
                    'handleFetchEmailsClick',
                    'fetchEmailsForAccount',
                    'fetchAggregatedEmails',
                    'apiRequest'
                ]
                
                print("   🔧 JavaScript函数检查:")
                for func in functions_to_check:
                    exists = func in js_content
                    status = "✅" if exists else "❌"
                    print(f"      {status} {func}")
                
                # 检查API_BASE_URL配置
                if 'API_BASE_URL' in js_content:
                    print("   ✅ API_BASE_URL已配置")
                else:
                    print("   ❌ API_BASE_URL未找到")
                
                return True
            else:
                print("   ❌ 无法加载JavaScript文件")
        else:
            print(f"   ❌ 前端服务异常: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 前端测试失败: {e}")
    
    return False

def check_common_issues():
    """检查常见问题"""
    print("\n6. 检查常见问题...")
    
    issues_found = []
    
    # 检查服务状态
    try:
        backend_response = requests.get(f"{API_BASE_URL}/api/accounts", timeout=5)
        if backend_response.status_code != 200:
            issues_found.append("后端服务异常")
    except:
        issues_found.append("后端服务无法连接")
    
    try:
        frontend_response = requests.get("http://localhost:3000", timeout=5)
        if frontend_response.status_code != 200:
            issues_found.append("前端服务异常")
    except:
        issues_found.append("前端服务无法连接")
    
    # 检查CORS配置
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        cors_header = response.headers.get('Access-Control-Allow-Origin')
        if not cors_header:
            issues_found.append("CORS配置缺失")
        elif cors_header != "http://localhost:3000":
            issues_found.append("CORS配置不正确")
    except:
        pass
    
    if issues_found:
        print("   ⚠️ 发现问题:")
        for issue in issues_found:
            print(f"      - {issue}")
    else:
        print("   ✅ 未发现常见问题")
    
    return issues_found

def main():
    """主诊断函数"""
    print("🔍 邮件获取功能诊断")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行诊断步骤
    accounts = test_backend_apis()
    
    if accounts:
        single_success = test_single_email_fetch(accounts)
        aggregated_success = test_aggregated_email_fetch(accounts)
        test_token_validity(accounts)
    else:
        single_success = False
        aggregated_success = False
    
    frontend_success = test_frontend_integration()
    issues = check_common_issues()
    
    # 生成诊断报告
    print("\n" + "=" * 60)
    print("📊 诊断报告")
    print("=" * 60)
    
    print("🔧 功能状态:")
    print(f"   后端账户API: {'✅ 正常' if accounts else '❌ 异常'}")
    print(f"   单账户邮件获取: {'✅ 正常' if single_success else '❌ 异常'}")
    print(f"   聚合邮件获取: {'✅ 正常' if aggregated_success else '❌ 异常'}")
    print(f"   前端集成: {'✅ 正常' if frontend_success else '❌ 异常'}")
    
    if issues:
        print("\n⚠️ 需要修复的问题:")
        for issue in issues:
            print(f"   - {issue}")
    
    print("\n💡 建议:")
    if not accounts:
        print("   1. 检查后端服务是否正常启动")
        print("   2. 确认数据库中有有效的账户数据")
    elif not single_success:
        print("   1. 检查refresh_token是否有效")
        print("   2. 验证邮箱账户权限设置")
        print("   3. 检查网络连接和防火墙设置")
    elif not frontend_success:
        print("   1. 重启前端服务")
        print("   2. 清除浏览器缓存")
        print("   3. 检查浏览器控制台错误")
    else:
        print("   ✅ 所有功能正常，问题可能是临时性的")
        print("   建议刷新页面或重试操作")

if __name__ == "__main__":
    main()
