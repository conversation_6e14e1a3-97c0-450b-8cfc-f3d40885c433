# 📧 多账户邮件聚合管理系统

一个现代化的全栈邮件管理解决方案，支持多个Microsoft账户的邮件批量获取和管理。

## 🎯 项目特性

- 🎨 **现代化前端**: Vue.js 3 + Vite构建的响应式界面
- ⚡ **高性能后端**: Flask + 异步处理，支持并发邮件获取
- 📊 **实时统计**: 邮件数据可视化和分析
- 📥 **批量导入**: 支持CSV/JSON格式的账户批量导入
- 🔄 **智能处理**: 自动区分不同域名的OAuth2端点
- 🌐 **跨域支持**: 完整的CORS配置，支持前后端分离部署

## 🏗️ 项目架构

```
┌─────────────────┐    API调用    ┌─────────────────┐
│   Vue.js前端     │ ──────────► │   Flask后端     │
│   (Netlify)     │             │   (自有服务器)   │
│   端口: 5173     │             │   端口: 8000     │
└─────────────────┘             └─────────────────┘
```

## 📁 项目结构

```
mail-aggregator/
├── frontend-vue/          # Vue.js前端应用
│   ├── src/components/    # Vue组件
│   ├── src/services/      # API服务层
│   ├── netlify.toml       # Netlify部署配置
│   └── package.json       # 前端依赖
├── backend/               # Flask后端API
│   ├── flask_main.py      # 主服务器
│   ├── mail_fetcher.py    # 邮件获取核心
│   ├── async_mail_fetcher.py  # 异步处理
│   ├── requirements.txt   # 后端依赖
│   └── tests/             # 测试套件
├── documentation/         # 项目文档
└── DEPLOYMENT_GUIDE.md    # 部署指南
```

## 🛠️ 技术栈

### 前端技术
- **Vue.js 3.2.37**: 现代化前端框架
- **Vite 3.0.7**: 快速构建工具
- **Composition API**: 现代Vue开发模式
- **响应式设计**: 适配多种设备

### 后端技术
- **Flask 3.1.1**: Web框架
- **Flask-CORS**: 跨域支持
- **异步处理**: concurrent.futures
- **SQLite**: 轻量级数据库
- **OAuth2**: Microsoft账户认证

## 🚀 快速开始

### 开发环境

#### 1. 克隆仓库
```bash
git clone https://github.com/your-username/mail-aggregator.git
cd mail-aggregator
```

#### 2. 启动后端
```bash
cd backend
pip install -r requirements.txt
python flask_main.py
```

#### 3. 启动前端
```bash
cd frontend-vue
npm install
npm run dev
```

#### 4. 访问应用
- 前端: http://localhost:5173
- 后端API: http://localhost:8000

### 生产部署

#### 前端部署到Netlify
1. 连接GitHub仓库到Netlify
2. 设置构建配置：
   - Base directory: `frontend-vue`
   - Build command: `npm run build`
   - Publish directory: `dist`

#### 后端部署到服务器
```bash
# 上传backend目录到服务器
scp -r backend/ user@your-server:/opt/mail-aggregator/

# 运行部署脚本
chmod +x /opt/mail-aggregator/deploy_server.sh
sudo /opt/mail-aggregator/deploy_server.sh
```

详细部署指南请参考 [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)

## 📊 功能特性

### 🎨 前端功能
- **账户管理**: 多选、批量删除、状态监控
- **邮件列表**: 分页、筛选、搜索
- **统计分析**: 实时数据可视化
- **导入导出**: CSV/JSON格式支持
- **响应式UI**: 适配桌面和移动设备

### ⚡ 后端功能
- **异步处理**: 并发获取多账户邮件
- **智能端点**: 自动选择正确的OAuth2端点
- **错误处理**: 完善的错误隔离和恢复
- **性能优化**: 连接池、缓存、批量处理
- **安全性**: CORS配置、输入验证

## 🔧 配置说明

### 环境变量

#### 前端环境变量
```bash
VITE_API_BASE_URL=http://localhost:8000  # 开发环境
VITE_API_BASE_URL=https://your-api.com  # 生产环境
```

#### 后端环境变量
```bash
FLASK_ENV=production
DATABASE_PATH=/app/data/accounts.db
FRONTEND_URL=https://your-app.netlify.app
LOG_LEVEL=INFO
```

### OAuth2配置
支持不同域名的Microsoft账户：
- `@outlook.com`: 使用common端点
- `@hotmail.com`: 使用consumers端点
- `@live.com`: 使用consumers端点

## 📚 文档

- [API文档](documentation/API_DOCUMENTATION.md)
- [前端部署指南](frontend-vue/DEPLOYMENT.md)
- [完整部署指南](DEPLOYMENT_GUIDE.md)
- [完整使用说明](documentation/完整使用说明文档.md)

## 🧪 测试

```bash
# 后端测试
cd backend
python -m pytest tests/

# 前端测试
cd frontend-vue
npm run test
```

## 📈 性能数据

- **并发处理**: 支持20+账户同时处理
- **处理速度**: 平均每账户1秒内完成
- **错误容忍**: 部分账户失败不影响整体
- **响应时间**: API响应时间<100ms

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 🔗 相关链接

- [Vue.js官方文档](https://vuejs.org/)
- [Flask官方文档](https://flask.palletsprojects.com/)
- [Microsoft Graph API](https://docs.microsoft.com/en-us/graph/)
- [Netlify部署文档](https://docs.netlify.com/)
