<template>
  <div class="app-container">
    <header class="app-header">
      <h1>📧 多账户邮件聚合管理系统</h1>
      <div>{{ connectionStatus ? '🟢 服务正常' : '🔴 服务异常' }}</div>
    </header>
    
    <main class="app-main">
      <div class="control-panel">
        <h2>账户列表 ({{ accounts.length }}个)</h2>
        <div v-if="isLoading">正在加载...</div>
        <div v-else>
          <div v-for="account in accounts" :key="account.id">
            {{ account.email_address }} - {{ account.status }}
          </div>
        </div>
        <button @click="loadAccounts" :disabled="isLoading">
          {{ isLoading ? '加载中...' : '刷新账户' }}
        </button>
      </div>
      
      <div class="email-panel">
        <h2>邮件列表</h2>
        <p>{{ statusMessage }}</p>
      </div>
      
      <div class="detail-panel">
        <h2>邮件详情</h2>
        <p>请选择邮件查看详情</p>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const accounts = ref([])
const isLoading = ref(false)
const connectionStatus = ref(true)
const statusMessage = ref('📭 请选择账户和文件夹，然后点击"获取邮件"')

const loadAccounts = async () => {
  try {
    isLoading.value = true
    const response = await fetch('http://localhost:8000/api/accounts')
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }
    accounts.value = await response.json()
    connectionStatus.value = true
  } catch (error) {
    console.error('加载账户失败:', error)
    connectionStatus.value = false
    statusMessage.value = `❌ 加载失败：${error.message}`
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadAccounts()
})
</script>

<style>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-main {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 400px;
  gap: 0;
}

.control-panel, .email-panel, .detail-panel {
  background: white;
  border-right: 1px solid #e2e8f0;
  padding: 1rem;
}

.detail-panel {
  border-right: none;
}

button {
  padding: 0.5rem 1rem;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>