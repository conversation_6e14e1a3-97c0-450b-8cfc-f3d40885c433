"""
数据库功能测试脚本
"""

from database import init_db, add_account, get_account_by_email, get_all_accounts, delete_account
from models import AccountCreate

def test_database():
    """测试数据库基本功能"""
    print("开始测试数据库功能...")
    
    # 初始化数据库
    init_db()
    print("✓ 数据库初始化完成")
    
    # 测试添加账户
    test_account = AccountCreate(
        email_address="<EMAIL>",
        client_id="test-client-id",
        refresh_token="test-refresh-token"
    )
    
    try:
        added_account = add_account(test_account)
        print(f"✓ 成功添加账户: {added_account.email_address}")
    except Exception as e:
        print(f"✗ 添加账户失败: {e}")
        return
    
    # 测试查询账户
    found_account = get_account_by_email("<EMAIL>")
    if found_account:
        print(f"✓ 成功查询账户: {found_account.email_address}")
    else:
        print("✗ 查询账户失败")
        return
    
    # 测试获取所有账户
    all_accounts = get_all_accounts()
    print(f"✓ 获取所有账户: 共 {len(all_accounts)} 个账户")
    
    # 测试删除账户
    if delete_account("<EMAIL>"):
        print("✓ 成功删除测试账户")
    else:
        print("✗ 删除账户失败")
    
    print("数据库功能测试完成！")

if __name__ == "__main__":
    test_database()
