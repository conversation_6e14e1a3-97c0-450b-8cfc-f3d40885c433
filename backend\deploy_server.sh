#!/bin/bash

# 后端服务器部署脚本

echo "🚀 开始部署后端服务器..."

# 设置变量
APP_DIR="/opt/mail-aggregator"
SERVICE_NAME="mail-aggregator"
USER="mail-app"

# 创建应用目录
echo "📁 创建应用目录..."
sudo mkdir -p $APP_DIR
sudo mkdir -p $APP_DIR/data
sudo mkdir -p $APP_DIR/logs

# 复制应用文件
echo "📋 复制应用文件..."
# 如果在backend目录中运行脚本
if [ -f "flask_main.py" ]; then
    sudo cp -r * $APP_DIR/
else
    # 如果在项目根目录运行脚本
    sudo cp -r backend/* $APP_DIR/
fi

# 安装Python依赖
echo "📦 安装Python依赖..."
cd $APP_DIR
sudo pip3 install -r requirements.txt

# 创建应用用户
echo "👤 创建应用用户..."
sudo useradd -r -s /bin/false $USER || true
sudo chown -R $USER:$USER $APP_DIR

# 创建systemd服务文件
echo "⚙️ 创建systemd服务..."
sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null <<EOF
[Unit]
Description=Mail Aggregator API Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_DIR
Environment=PATH=/usr/bin:/usr/local/bin
Environment=PYTHONPATH=$APP_DIR
Environment=FLASK_ENV=production
Environment=DATABASE_PATH=$APP_DIR/data/accounts.db
Environment=LOG_FILE=$APP_DIR/logs/app.log
Environment=FRONTEND_URL=https://your-app.netlify.app
ExecStart=/usr/bin/python3 $APP_DIR/start_production.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
echo "🔄 重新加载systemd..."
sudo systemctl daemon-reload

# 启用并启动服务
echo "▶️ 启动服务..."
sudo systemctl enable $SERVICE_NAME
sudo systemctl start $SERVICE_NAME

# 检查服务状态
echo "📊 检查服务状态..."
sudo systemctl status $SERVICE_NAME

# 配置防火墙（如果使用ufw）
echo "🔥 配置防火墙..."
sudo ufw allow 8000/tcp || true

# 显示部署信息
echo ""
echo "✅ 部署完成！"
echo "📊 服务状态: sudo systemctl status $SERVICE_NAME"
echo "📋 查看日志: sudo journalctl -u $SERVICE_NAME -f"
echo "🔄 重启服务: sudo systemctl restart $SERVICE_NAME"
echo "⏹️ 停止服务: sudo systemctl stop $SERVICE_NAME"
echo ""
echo "🌐 API地址: http://your-server-ip:8000"
echo "📝 请更新前端配置中的API地址"
