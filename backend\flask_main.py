"""
Flask 版本的多账户邮件聚合 API 服务
"""

import logging
import sqlite3
import json
from flask import Flask, request, jsonify
from flask_cors import CORS
from typing import Dict, Any

# 导入自定义模块
from database import init_db, add_account, get_account_by_email, get_all_accounts, delete_account, update_account_status
from models import AccountCreate, AggregateMailRequest
from mail_fetcher import get_emails_for_account, get_emails_for_single_folder
from async_mail_fetcher import async_mail_fetcher
import asyncio

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 Flask 应用实例
app = Flask(__name__)

# 配置 CORS 支持
CORS(app, origins=["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"],
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
     allow_headers=["Content-Type", "Authorization"])

# 初始化数据库
try:
    init_db()
    logger.info("数据库初始化完成")
except Exception as e:
    logger.error(f"数据库初始化失败: {e}")
    raise


@app.route("/", methods=["GET"])
def root():
    """根路径 - API 服务状态"""
    return jsonify({
        "message": "多账户邮件聚合 API 服务",
        "status": "运行中",
        "version": "1.0.0",
        "endpoints": {
            "accounts": "/api/accounts",
            "docs": "请查看源代码了解 API 接口"
        }
    })


@app.route("/api/accounts", methods=["POST"])
def create_account():
    """
    添加新的邮箱账户
    """
    try:
        # 获取 JSON 数据
        if not request.is_json:
            return jsonify({
                "error": "INVALID_CONTENT_TYPE",
                "message": "请求必须是 JSON 格式",
                "status_code": 400
            }), 400
        
        account_data = request.get_json()
        
        # 验证必需字段
        required_fields = ["email_address", "client_id", "refresh_token"]
        for field in required_fields:
            if field not in account_data:
                return jsonify({
                    "error": "MISSING_FIELD",
                    "message": f"缺少必需字段: {field}",
                    "status_code": 400
                }), 400
        
        # 检查邮箱是否已存在
        existing_account = get_account_by_email(account_data["email_address"])
        if existing_account:
            return jsonify({
                "error": "ACCOUNT_EXISTS",
                "message": f"邮箱地址 {account_data['email_address']} 已存在",
                "status_code": 409
            }), 409
        
        # 创建 AccountCreate 对象
        account_create = AccountCreate(
            email_address=account_data["email_address"],
            client_id=account_data["client_id"],
            refresh_token=account_data["refresh_token"]
        )
        
        # 添加新账户
        new_account = add_account(account_create)
        
        # 返回响应（不包含 refresh_token）
        return jsonify({
            "id": new_account.id,
            "email_address": new_account.email_address,
            "client_id": new_account.client_id,
            "status": new_account.status,
            "last_updated": new_account.last_updated
        }), 201
        
    except sqlite3.IntegrityError:
        return jsonify({
            "error": "ACCOUNT_EXISTS",
            "message": f"邮箱地址 {account_data['email_address']} 已存在",
            "status_code": 409
        }), 409
    except Exception as e:
        logger.error(f"创建账户时发生错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"创建账户失败: {str(e)}",
            "status_code": 500
        }), 500


@app.route("/api/accounts", methods=["GET"])
def get_accounts():
    """
    获取所有账户信息
    """
    try:
        accounts = get_all_accounts()
        return jsonify(accounts)
        
    except Exception as e:
        logger.error(f"获取账户列表时发生错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"获取账户列表失败: {str(e)}",
            "status_code": 500
        }), 500


@app.route("/api/accounts/<email_address>", methods=["DELETE"])
def delete_account_by_email(email_address: str):
    """
    删除指定邮箱的账户
    """
    try:
        # 检查账户是否存在
        existing_account = get_account_by_email(email_address)
        if not existing_account:
            return jsonify({
                "error": "ACCOUNT_NOT_FOUND",
                "message": f"邮箱地址 {email_address} 不存在",
                "status_code": 404
            }), 404
        
        # 删除账户
        success = delete_account(email_address)
        
        if success:
            return jsonify({
                "message": f"成功删除账户: {email_address}",
                "email_address": email_address
            })
        else:
            return jsonify({
                "error": "DELETE_FAILED",
                "message": "删除账户失败",
                "status_code": 500
            }), 500
            
    except Exception as e:
        logger.error(f"删除账户时发生错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"删除账户失败: {str(e)}",
            "status_code": 500
        }), 500


@app.route("/api/accounts/<email_address>", methods=["GET"])
def get_account_info(email_address: str):
    """
    获取指定邮箱的账户信息
    """
    try:
        account = get_account_by_email(email_address)
        
        if not account:
            return jsonify({
                "error": "ACCOUNT_NOT_FOUND",
                "message": f"邮箱地址 {email_address} 不存在",
                "status_code": 404
            }), 404
        
        return jsonify({
            "id": account.id,
            "email_address": account.email_address,
            "client_id": account.client_id,
            "status": account.status,
            "last_updated": account.last_updated
        })
        
    except Exception as e:
        logger.error(f"获取账户信息时发生错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"获取账户信息失败: {str(e)}",
            "status_code": 500
        }), 500


@app.route("/api/mail/<email_address>", methods=["GET"])
def get_emails(email_address: str):
    """
    获取指定账户的邮件 (支持多文件夹)

    查询参数:
        folders: 邮箱文件夹列表，逗号分隔 (默认: inbox)
        folder: 单个邮箱文件夹 (向后兼容，默认: inbox)
        limit: 每个文件夹的邮件数量 (默认: 5)
    """
    try:
        # 获取查询参数 - 支持新的 folders 参数和向后兼容的 folder 参数
        folders_param = request.args.get('folders')
        folder_param = request.args.get('folder')
        limit = int(request.args.get('limit', 5))

        # 处理文件夹参数
        if folders_param:
            # 新的多文件夹模式
            folders = [f.strip() for f in folders_param.split(',') if f.strip()]
            if not folders:
                folders = ['inbox']
        elif folder_param:
            # 向后兼容的单文件夹模式
            folders = [folder_param]
        else:
            # 默认值
            folders = ['inbox']

        # 验证 limit 参数
        if limit < 1 or limit > 500:
            return jsonify({
                "error": "INVALID_LIMIT",
                "message": "limit 参数必须在 1-500 之间",
                "status_code": 400
            }), 400

        # 检查账户是否存在
        account = get_account_by_email(email_address)
        if not account:
            return jsonify({
                "error": "ACCOUNT_NOT_FOUND",
                "message": f"邮箱地址 {email_address} 不存在",
                "status_code": 404
            }), 404

        logger.info(f"开始获取邮件: {email_address}, folders: {folders}, limit: {limit}")

        # 获取邮件 - 使用新的多文件夹函数
        emails_result = get_emails_for_account(account, folders, limit)

        # 检查结果类型
        if isinstance(emails_result, dict) and "error_key" in emails_result:
            # 邮件获取失败，更新账户状态
            error_key = emails_result["error_key"]
            error_msg = emails_result["error_msg"]

            # 根据错误类型更新状态
            if "Token失效" in error_key or "Token" in error_msg:
                update_account_status(email_address, "Token Invalid")
                status_code = 401
            elif "IMAP" in error_key or "登录失败" in error_key:
                update_account_status(email_address, "Login Failed")
                status_code = 403
            else:
                update_account_status(email_address, "Error")
                status_code = 500

            logger.error(f"邮件获取失败: {email_address} - {error_msg}")

            return jsonify({
                "error": error_key.upper().replace(" ", "_"),
                "message": error_msg,
                "email_address": email_address,
                "status_code": status_code
            }), status_code

        elif isinstance(emails_result, list):
            # 邮件获取成功，更新账户状态
            update_account_status(email_address, "OK")

            logger.info(f"成功获取 {len(emails_result)} 封邮件: {email_address}")

            return jsonify({
                "email_address": email_address,
                "folders": folders,  # 返回文件夹列表
                "total_count": len(emails_result),
                "messages": emails_result
            })

        else:
            # 未知错误
            logger.error(f"未知的邮件获取结果类型: {type(emails_result)}")
            return jsonify({
                "error": "UNKNOWN_ERROR",
                "message": "邮件获取返回了未知的结果类型",
                "status_code": 500
            }), 500

    except ValueError as e:
        return jsonify({
            "error": "INVALID_PARAMETER",
            "message": f"参数错误: {str(e)}",
            "status_code": 400
        }), 400
    except Exception as e:
        logger.error(f"获取邮件时发生未知错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"获取邮件失败: {str(e)}",
            "status_code": 500
        }), 500


@app.route("/api/mail/aggregate", methods=["POST"])
def aggregate_emails():
    """
    聚合邮件获取接口 - 支持多账户多文件夹批量获取

    请求体:
    {
        "accounts": ["<EMAIL>", "<EMAIL>"],  // 账户列表，空数组表示所有账户
        "folders": ["inbox", "junk", "sent"],
        "limit": 10
    }
    """
    try:
        # 获取 JSON 数据
        if not request.is_json:
            return jsonify({
                "error": "INVALID_CONTENT_TYPE",
                "message": "请求必须是 JSON 格式",
                "status_code": 400
            }), 400

        request_data = request.get_json()

        # 获取参数
        accounts = request_data.get('accounts', [])
        folders = request_data.get('folders', ['inbox'])
        limit = request_data.get('limit', 5)

        # 如果没有指定账户，获取所有账户
        if not accounts:
            all_accounts = get_all_accounts()
            accounts = [acc['email_address'] for acc in all_accounts]

        # 验证账户列表
        if not isinstance(accounts, list):
            return jsonify({
                "error": "INVALID_ACCOUNTS",
                "message": "accounts 必须是字符串列表",
                "status_code": 400
            }), 400

        # 验证 folders 参数
        if not isinstance(folders, list) or not folders:
            return jsonify({
                "error": "INVALID_FOLDERS",
                "message": "folders 必须是非空的字符串列表",
                "status_code": 400
            }), 400

        # 验证 limit 参数
        if not isinstance(limit, int) or limit < 1 or limit > 500:
            return jsonify({
                "error": "INVALID_LIMIT",
                "message": "limit 参数必须是 1-500 之间的整数",
                "status_code": 400
            }), 400

        logger.info(f"聚合获取邮件: accounts={accounts}, folders={folders}, limit={limit}")

        # 聚合多个账户的邮件
        all_messages = []
        total_count = 0
        successful_accounts = []
        failed_accounts = []

        for email_address in accounts:
            try:
                # 检查账户是否存在
                account = get_account_by_email(email_address)
                if not account:
                    logger.warning(f"账户不存在: {email_address}")
                    failed_accounts.append({
                        "email": email_address,
                        "error": "账户不存在"
                    })
                    continue

                # 获取该账户的邮件
                emails_result = get_emails_for_account(account, folders, limit)

                # 检查结果类型
                if isinstance(emails_result, dict) and "error_key" in emails_result:
                    # 邮件获取失败
                    error_key = emails_result["error_key"]
                    error_msg = emails_result["error_msg"]

                    logger.error(f"账户 {email_address} 邮件获取失败: {error_msg}")
                    failed_accounts.append({
                        "email": email_address,
                        "error": error_msg
                    })

                    # 更新账户状态
                    if "Token失效" in error_key or "Token" in error_msg:
                        update_account_status(email_address, "Token Invalid")
                    elif "IMAP" in error_key or "登录失败" in error_key:
                        update_account_status(email_address, "Login Failed")
                    else:
                        update_account_status(email_address, "Error")

                    continue

                # 成功获取邮件
                if isinstance(emails_result, list):
                    # emails_result 直接是邮件列表
                    messages = emails_result
                    count = len(messages)

                    # 为每封邮件添加账户信息
                    for message in messages:
                        message["account"] = email_address

                    all_messages.extend(messages)
                    total_count += count
                    successful_accounts.append(email_address)

                    logger.info(f"账户 {email_address} 成功获取 {len(messages)} 封邮件")

                    # 更新账户状态为正常
                    update_account_status(email_address, "OK")
                else:
                    # 未知的返回类型
                    logger.error(f"账户 {email_address} 返回了未知的结果类型: {type(emails_result)}")
                    failed_accounts.append({
                        "email": email_address,
                        "error": f"未知的结果类型: {type(emails_result)}"
                    })

            except Exception as e:
                logger.error(f"处理账户 {email_address} 时发生异常: {e}")
                failed_accounts.append({
                    "email": email_address,
                    "error": str(e)
                })

        # 按时间排序所有邮件（最新的在前）
        all_messages.sort(key=lambda x: x.get('date', ''), reverse=True)

        # 构建响应数据
        response_data = {
            "accounts": accounts,
            "folders": folders,
            "total_count": total_count,
            "messages": all_messages,
            "summary": {
                "total_accounts": len(accounts),
                "successful_accounts": len(successful_accounts),
                "failed_accounts": len(failed_accounts),
                "total_messages": len(all_messages)
            }
        }

        # 如果有失败的账户，添加失败信息
        if failed_accounts:
            response_data["failed_accounts"] = failed_accounts

        # 如果有成功的账户，添加成功信息
        if successful_accounts:
            response_data["successful_accounts"] = successful_accounts

        logger.info(f"聚合完成: 成功 {len(successful_accounts)} 个账户，失败 {len(failed_accounts)} 个账户，共获取 {len(all_messages)} 封邮件")

        # 根据结果决定HTTP状态码
        if not successful_accounts:
            # 所有账户都失败了
            return jsonify({
                "error": "ALL_ACCOUNTS_FAILED",
                "message": "所有账户的邮件获取都失败了",
                "failed_accounts": failed_accounts,
                "status_code": 500
            }), 500
        elif failed_accounts:
            # 部分成功，部分失败
            return jsonify(response_data), 206  # 206 Partial Content
        else:
            # 全部成功
            return jsonify(response_data), 200

    except Exception as e:
        logger.error(f"聚合获取邮件时发生未知错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"聚合获取邮件失败: {str(e)}",
            "status_code": 500
        }), 500


# 全局错误处理器
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "error": "NOT_FOUND",
        "message": "请求的资源不存在",
        "status_code": 404
    }), 404


@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        "error": "METHOD_NOT_ALLOWED",
        "message": "请求方法不被允许",
        "status_code": 405
    }), 405


@app.errorhandler(500)
def internal_server_error(error):
    return jsonify({
        "error": "INTERNAL_SERVER_ERROR",
        "message": "服务器内部错误",
        "status_code": 500
    }), 500


@app.route("/api/mail/aggregate-async", methods=["POST"])
def aggregate_emails_async():
    """
    异步聚合邮件获取接口 - 支持多账户多文件夹并发获取
    
    请求体:
    {
        "accounts": ["<EMAIL>", "<EMAIL>"],  // 账户列表，空数组表示所有账户
        "folders": ["inbox", "junk", "sent"],
        "limit": 10
    }
    """
    try:
        # 获取 JSON 数据
        if not request.is_json:
            return jsonify({
                "error": "INVALID_CONTENT_TYPE",
                "message": "请求必须是 JSON 格式",
                "status_code": 400
            }), 400

        request_data = request.get_json()

        # 获取参数
        accounts = request_data.get('accounts', [])
        folders = request_data.get('folders', ['inbox'])
        limit = request_data.get('limit', 5)

        # 如果没有指定账户，获取所有账户
        if not accounts:
            all_accounts = get_all_accounts()
            accounts = [acc['email_address'] for acc in all_accounts]

        # 验证账户列表
        if not isinstance(accounts, list):
            return jsonify({
                "error": "INVALID_ACCOUNTS",
                "message": "accounts 必须是字符串列表",
                "status_code": 400
            }), 400

        # 验证 folders 参数
        if not isinstance(folders, list) or not folders:
            return jsonify({
                "error": "INVALID_FOLDERS",
                "message": "folders 必须是非空的字符串列表",
                "status_code": 400
            }), 400

        # 验证 limit 参数
        if not isinstance(limit, int) or limit < 1 or limit > 500:
            return jsonify({
                "error": "INVALID_LIMIT",
                "message": "limit 参数必须是 1-500 之间的整数",
                "status_code": 400
            }), 400

        logger.info(f"异步聚合获取邮件: accounts={accounts}, folders={folders}, limit={limit}")

        # 获取账户详细信息
        account_details = []
        missing_accounts = []
        
        for email_address in accounts:
            account = get_account_by_email(email_address)
            if not account:
                logger.warning(f"账户不存在: {email_address}")
                missing_accounts.append(email_address)
                continue
            account_details.append(account)

        if not account_details:
            return jsonify({
                "error": "NO_VALID_ACCOUNTS",
                "message": "没有找到有效的账户",
                "missing_accounts": missing_accounts,
                "status_code": 400
            }), 400

        # 使用异步获取邮件
        try:
            result = asyncio.run(async_mail_fetcher.fetch_emails_batch_async(
                account_details, folders, limit
            ))
            
            # 更新账户状态
            for email_address in result["successful_accounts"]:
                update_account_status(email_address, "OK")
            
            for failed_account in result["failed_accounts"]:
                email_address = failed_account["email"]
                error_msg = failed_account["error"]
                
                # 根据错误类型更新状态
                if "Token失效" in error_msg or "Token" in error_msg:
                    update_account_status(email_address, "Token Invalid")
                elif "IMAP" in error_msg or "登录失败" in error_msg:
                    update_account_status(email_address, "Login Failed")
                else:
                    update_account_status(email_address, "Error")

            logger.info(f"异步聚合获取完成: 总邮件 {result['total_count']} 封, 耗时 {result['duration']:.2f} 秒")

            return jsonify({
                "accounts": accounts,
                "folders": result["folders"],
                "total_count": result["total_count"],
                "messages": result["messages"],
                "successful_accounts": result["successful_accounts"],
                "failed_accounts": result["failed_accounts"],
                "missing_accounts": missing_accounts,
                "performance": {
                    "duration": result["duration"],
                    "accounts_processed": len(account_details),
                    "concurrent_processing": True
                }
            })

        except Exception as e:
            logger.error(f"异步邮件获取过程出错: {e}")
            return jsonify({
                "error": "ASYNC_FETCH_ERROR",
                "message": f"异步获取邮件失败: {str(e)}",
                "status_code": 500
            }), 500

    except ValueError as e:
        return jsonify({
            "error": "INVALID_JSON",
            "message": f"JSON 格式错误: {str(e)}",
            "status_code": 400
        }), 400
    except Exception as e:
        logger.error(f"聚合邮件接口发生未知错误: {e}")
        return jsonify({
            "error": "INTERNAL_SERVER_ERROR",
            "message": f"服务器内部错误: {str(e)}",
            "status_code": 500
        }), 500


if __name__ == "__main__":
    logger.info("启动 Flask 应用...")
    app.run(
        host="0.0.0.0",
        port=8000,
        debug=True
    )
