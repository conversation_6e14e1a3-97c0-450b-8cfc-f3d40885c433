"""
简单的token有效性测试
"""

import requests

def test_token_request():
    """测试一个示例token请求"""
    print("🔍 测试token请求格式")
    print("=" * 50)
    
    # 使用示例数据测试请求格式
    test_data = {
        'client_id': 'example-client-id',
        'grant_type': 'refresh_token',
        'refresh_token': 'example-refresh-token'
    }
    
    endpoints = {
        'consumers': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token',
        'common': 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
    }
    
    for name, url in endpoints.items():
        print(f"\n📡 测试端点: {name}")
        print(f"   URL: {url}")
        
        try:
            response = requests.post(url, data=test_data, timeout=10)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 400:
                try:
                    error_data = response.json()
                    error_code = error_data.get('error', 'unknown')
                    error_desc = error_data.get('error_description', '')
                    print(f"   错误代码: {error_code}")
                    print(f"   错误描述: {error_desc[:100]}...")
                    
                    if error_code == 'invalid_client':
                        print(f"   💡 这是预期的 - 示例client_id无效")
                    elif error_code == 'invalid_grant':
                        print(f"   💡 这是预期的 - 示例refresh_token无效")
                        
                except Exception as e:
                    print(f"   无法解析错误响应: {e}")
                    
        except Exception as e:
            print(f"   请求异常: {e}")

if __name__ == "__main__":
    test_token_request()
    
    print("\n" + "=" * 50)
    print("📋 结论:")
    print("   1. 如果看到 'invalid_client' 或 'invalid_grant' 错误")
    print("   2. 说明端点可以访问，问题在于token本身")
    print("   3. 需要获取有效的client_id和refresh_token")
    print("   4. hotmail账户现在使用正确的consumers端点")
