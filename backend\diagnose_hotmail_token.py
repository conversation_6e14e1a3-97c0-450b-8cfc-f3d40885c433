"""
诊断hotmail账户token问题
获取详细的错误响应信息
"""

import requests
import json
from database import get_all_accounts

def diagnose_hotmail_token():
    """诊断hotmail账户的token问题"""
    print("🔍 诊断hotmail账户token问题")
    print("=" * 60)
    
    # 获取所有账户
    accounts = get_all_accounts()
    hotmail_accounts = [acc for acc in accounts if acc.email_address.endswith('@hotmail.com')]
    
    if not hotmail_accounts:
        print("❌ 没有找到hotmail账户")
        return
    
    print(f"📧 找到 {len(hotmail_accounts)} 个hotmail账户")
    
    # 测试第一个hotmail账户
    test_account = hotmail_accounts[0]
    print(f"\n🎯 测试账户: {test_account.email_address}")
    
    # 构造token请求
    token_url = "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
    
    data = {
        'client_id': test_account.client_id,
        'grant_type': 'refresh_token',
        'refresh_token': test_account.refresh_token,
    }
    
    print(f"📡 请求端点: {token_url}")
    print(f"📋 Client ID: {test_account.client_id[:20]}...")
    print(f"📋 Refresh Token: {test_account.refresh_token[:30]}...")
    
    try:
        print("\n🔄 发送token请求...")
        response = requests.post(token_url, data=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📄 响应内容:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if 'error' in response_data:
                error_code = response_data.get('error')
                error_desc = response_data.get('error_description', '')
                
                print(f"\n❌ 错误分析:")
                print(f"   错误代码: {error_code}")
                print(f"   错误描述: {error_desc}")
                
                # 分析常见错误
                if error_code == 'invalid_grant':
                    print(f"\n💡 解决建议:")
                    print(f"   - refresh_token已过期或无效")
                    print(f"   - 需要重新获取有效的refresh_token")
                elif error_code == 'invalid_client':
                    print(f"\n💡 解决建议:")
                    print(f"   - client_id无效或不匹配")
                    print(f"   - 检查Azure应用注册配置")
                elif error_code == 'invalid_request':
                    print(f"\n💡 解决建议:")
                    print(f"   - 请求参数格式错误")
                    print(f"   - 检查grant_type和其他参数")
                    
        except json.JSONDecodeError:
            print(f"📄 响应内容 (非JSON):")
            print(response.text[:500])
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def compare_endpoints():
    """比较不同端点的响应"""
    print(f"\n🔄 比较不同OAuth2端点")
    print("=" * 60)
    
    accounts = get_all_accounts()
    hotmail_accounts = [acc for acc in accounts if acc.email_address.endswith('@hotmail.com')]
    
    if not hotmail_accounts:
        print("❌ 没有找到hotmail账户")
        return
        
    test_account = hotmail_accounts[0]
    
    endpoints = {
        'consumers': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token',
        'common': 'https://login.microsoftonline.com/common/oauth2/v2.0/token'
    }
    
    data = {
        'client_id': test_account.client_id,
        'grant_type': 'refresh_token', 
        'refresh_token': test_account.refresh_token,
    }
    
    for name, url in endpoints.items():
        print(f"\n📡 测试端点: {name} ({url})")
        
        try:
            response = requests.post(url, data=data, timeout=30)
            print(f"   状态码: {response.status_code}")
            
            if response.status_code != 200:
                try:
                    error_data = response.json()
                    error_code = error_data.get('error', 'unknown')
                    print(f"   错误: {error_code}")
                except:
                    print(f"   错误: 无法解析响应")
            else:
                print(f"   ✅ 成功获取token")
                
        except Exception as e:
            print(f"   ❌ 请求失败: {e}")

def check_account_details():
    """检查账户详细信息"""
    print(f"\n📋 检查账户详细信息")
    print("=" * 60)
    
    accounts = get_all_accounts()
    hotmail_accounts = [acc for acc in accounts if acc.email_address.endswith('@hotmail.com')]
    
    for i, acc in enumerate(hotmail_accounts[:3], 1):
        print(f"\n{i}. {acc.email_address}")
        print(f"   状态: {acc.status}")
        print(f"   Client ID长度: {len(acc.client_id)}")
        print(f"   Refresh Token长度: {len(acc.refresh_token)}")
        print(f"   最后更新: {acc.last_updated}")
        
        # 检查token格式
        if not acc.refresh_token.startswith(('M.', '0.', '1.')):
            print(f"   ⚠️ Refresh Token格式可能不正确")
        
        if len(acc.client_id) != 36:
            print(f"   ⚠️ Client ID长度不标准 (应为36字符)")

if __name__ == "__main__":
    print("🚀 开始诊断hotmail账户token问题")
    
    diagnose_hotmail_token()
    compare_endpoints()
    check_account_details()
    
    print("\n" + "=" * 60)
    print("✅ 诊断完成")
    print("\n📋 总结:")
    print("   1. 修复已生效 - hotmail账户使用consumers端点")
    print("   2. 400错误通常表示token本身有问题")
    print("   3. 需要检查refresh_token是否有效")
    print("   4. 可能需要重新获取有效的token")
