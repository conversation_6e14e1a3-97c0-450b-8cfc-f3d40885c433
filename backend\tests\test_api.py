"""
API 接口测试脚本
测试账户管理相关的 API 接口
"""

import requests
import json
import time
from typing import Dict, Any

# API 基础 URL
BASE_URL = "http://localhost:8000"

def test_api_endpoints():
    """测试所有 API 接口"""
    print("开始测试 API 接口...")
    print(f"API 服务地址: {BASE_URL}")
    print("=" * 50)
    
    # 测试数据
    test_account = {
        "email_address": "<EMAIL>",
        "client_id": "test-client-id-12345",
        "refresh_token": "test-refresh-token-67890"
    }
    
    try:
        # 1. 测试根路径
        print("1. 测试根路径 GET /")
        response = requests.get(f"{BASE_URL}/")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 2. 测试获取所有账户（初始应该为空）
        print("2. 测试获取所有账户 GET /api/accounts")
        response = requests.get(f"{BASE_URL}/api/accounts")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 3. 测试添加账户
        print("3. 测试添加账户 POST /api/accounts")
        response = requests.post(
            f"{BASE_URL}/api/accounts",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 4. 测试重复添加账户（应该失败）
        print("4. 测试重复添加账户（应该返回 409 冲突）")
        response = requests.post(
            f"{BASE_URL}/api/accounts",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 5. 测试获取指定账户信息
        print("5. 测试获取指定账户信息 GET /api/accounts/{email}")
        response = requests.get(f"{BASE_URL}/api/accounts/{test_account['email_address']}")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 6. 测试获取不存在的账户
        print("6. 测试获取不存在的账户（应该返回 404）")
        response = requests.get(f"{BASE_URL}/api/accounts/<EMAIL>")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 7. 再次测试获取所有账户（应该有一个账户）
        print("7. 再次测试获取所有账户 GET /api/accounts")
        response = requests.get(f"{BASE_URL}/api/accounts")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 8. 测试删除账户
        print("8. 测试删除账户 DELETE /api/accounts/{email}")
        response = requests.delete(f"{BASE_URL}/api/accounts/{test_account['email_address']}")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 9. 测试删除不存在的账户
        print("9. 测试删除不存在的账户（应该返回 404）")
        response = requests.delete(f"{BASE_URL}/api/accounts/<EMAIL>")
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        print()
        
        # 10. 最后测试获取所有账户（应该为空）
        print("10. 最后测试获取所有账户（应该为空）")
        response = requests.get(f"{BASE_URL}/api/accounts")
        print(f"    状态码: {response.status_code}")
        print(f"    响应: {response.json()}")
        print()
        
        print("=" * 50)
        print("✓ API 接口测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到 API 服务器")
        print("请确保 API 服务器正在运行：python main.py")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")


def test_invalid_data():
    """测试无效数据的处理"""
    print("\n" + "=" * 50)
    print("测试无效数据处理...")
    
    # 测试无效的邮箱格式
    invalid_account = {
        "email_address": "invalid-email",
        "client_id": "test-client-id",
        "refresh_token": "test-refresh-token"
    }
    
    try:
        print("测试无效邮箱格式...")
        response = requests.post(
            f"{BASE_URL}/api/accounts",
            json=invalid_account,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到 API 服务器")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    print("API 接口测试工具")
    print("注意：请确保 API 服务器正在运行（python flask_main.py）")
    print()

    # 运行测试
    test_api_endpoints()
    test_invalid_data()
