"""
调试聚合邮件API问题
"""

import requests
import json
import time

# API 基础 URL
API_BASE_URL = "http://localhost:8000"

def test_single_account_detailed():
    """详细测试单个账户"""
    print("🔍 详细测试单个账户")
    print("=" * 50)
    
    # 获取账户列表
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        accounts = response.json()
        
        if not accounts:
            print("   ❌ 没有账户可测试")
            return None
        
        test_account = accounts[0]
        email = test_account['email_address']
        
        print(f"   🎯 测试账户: {email}")
        print(f"   📊 账户状态: {test_account['status']}")
        print(f"   🔑 Client ID: {test_account['client_id']}")

        # 检查是否有refresh_token字段
        if 'refresh_token' in test_account:
            print(f"   🎫 Refresh Token: {test_account['refresh_token'][:50]}...")
        else:
            print(f"   ⚠️ 账户数据缺少refresh_token字段")
            print(f"   📋 可用字段: {list(test_account.keys())}")
            print("   💡 这是正常的，API不返回敏感信息")
        
        # 测试邮件获取
        print("\n   📧 测试邮件获取...")
        start_time = time.time()
        
        mail_response = requests.get(
            f"{API_BASE_URL}/api/mail/{email}?folders=inbox&limit=3",
            timeout=30
        )
        
        end_time = time.time()
        print(f"   ⏱️ 响应时间: {end_time - start_time:.2f}秒")
        print(f"   📊 状态码: {mail_response.status_code}")
        
        if mail_response.status_code == 200:
            data = mail_response.json()
            messages = data.get('messages', [])
            print(f"   ✅ 成功获取 {len(messages)} 封邮件")
            
            if messages:
                print("   📧 邮件详情:")
                for i, msg in enumerate(messages[:2], 1):
                    print(f"      {i}. 主题: {msg.get('subject', 'N/A')[:30]}...")
                    print(f"         发件人: {msg.get('from', 'N/A')}")
                    print(f"         时间: {msg.get('date', 'N/A')}")
            
            return email
        else:
            print(f"   ❌ 邮件获取失败")
            print(f"   错误信息: {mail_response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return None

def test_aggregate_api_step_by_step():
    """逐步测试聚合API"""
    print("\n🔧 逐步测试聚合API")
    print("=" * 50)
    
    # 获取账户列表
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        accounts = response.json()
        
        if len(accounts) < 2:
            print("   ⚠️ 账户数量不足，使用单个账户测试")
            test_accounts = [accounts[0]['email_address']] if accounts else []
        else:
            test_accounts = [acc['email_address'] for acc in accounts[:2]]
        
        print(f"   🎯 测试账户: {test_accounts}")
        
        # 构建请求数据
        payload = {
            "accounts": test_accounts,
            "folders": ["inbox"],
            "limit": 3
        }
        
        print(f"\n   📤 请求数据:")
        print(json.dumps(payload, indent=4, ensure_ascii=False))
        
        # 发送请求
        print(f"\n   🚀 发送聚合请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{API_BASE_URL}/api/mail/aggregate",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        end_time = time.time()
        
        print(f"   ⏱️ 响应时间: {end_time - start_time:.2f}秒")
        print(f"   📊 状态码: {response.status_code}")
        print(f"   📋 响应头: {dict(response.headers)}")
        
        # 解析响应
        try:
            response_data = response.json()
            print(f"\n   📥 响应数据:")
            print(json.dumps(response_data, indent=4, ensure_ascii=False))
            
            if response.status_code == 200:
                messages = response_data.get('messages', [])
                summary = response_data.get('summary', {})
                
                print(f"\n   ✅ 聚合成功!")
                print(f"   📊 统计信息:")
                print(f"      总账户数: {summary.get('total_accounts', 0)}")
                print(f"      成功账户数: {summary.get('successful_accounts', 0)}")
                print(f"      失败账户数: {summary.get('failed_accounts', 0)}")
                print(f"      总邮件数: {summary.get('total_messages', 0)}")
                
                if messages:
                    print(f"\n   📧 邮件样本:")
                    for i, msg in enumerate(messages[:2], 1):
                        print(f"      {i}. 账户: {msg.get('account', 'N/A')}")
                        print(f"         主题: {msg.get('subject', 'N/A')[:30]}...")
                        print(f"         时间: {msg.get('date', 'N/A')}")
                
                return True
                
            elif response.status_code == 206:
                print(f"\n   ⚠️ 部分成功 (206)")
                summary = response_data.get('summary', {})
                failed_accounts = response_data.get('failed_accounts', [])
                
                print(f"   📊 统计信息:")
                print(f"      成功账户数: {summary.get('successful_accounts', 0)}")
                print(f"      失败账户数: {summary.get('failed_accounts', 0)}")
                
                if failed_accounts:
                    print(f"\n   ❌ 失败账户:")
                    for failed in failed_accounts:
                        print(f"      - {failed.get('email', 'N/A')}: {failed.get('error', 'N/A')}")
                
                return True
                
            else:
                print(f"\n   ❌ 聚合失败")
                error_msg = response_data.get('message', '未知错误')
                print(f"   错误信息: {error_msg}")
                
                if 'failed_accounts' in response_data:
                    failed_accounts = response_data['failed_accounts']
                    print(f"\n   ❌ 失败详情:")
                    for failed in failed_accounts:
                        print(f"      - {failed.get('email', 'N/A')}: {failed.get('error', 'N/A')}")
                
                return False
                
        except json.JSONDecodeError:
            print(f"\n   ❌ 响应不是有效的JSON")
            print(f"   原始响应: {response.text[:200]}...")
            return False
            
    except requests.exceptions.Timeout:
        print(f"   ❌ 请求超时")
        return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_empty_accounts_list():
    """测试空账户列表（应该获取所有账户）"""
    print("\n🌐 测试空账户列表（获取所有账户）")
    print("=" * 50)
    
    payload = {
        "accounts": [],  # 空列表，应该获取所有账户
        "folders": ["inbox"],
        "limit": 2
    }
    
    print(f"   📤 请求数据:")
    print(json.dumps(payload, indent=4, ensure_ascii=False))
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/mail/aggregate",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=90
        )
        
        print(f"   📊 状态码: {response.status_code}")
        
        if response.status_code in [200, 206]:
            data = response.json()
            summary = data.get('summary', {})
            
            print(f"   ✅ 空账户列表测试成功")
            print(f"   📊 处理了 {summary.get('total_accounts', 0)} 个账户")
            print(f"   📧 获取了 {summary.get('total_messages', 0)} 封邮件")
            
            return True
        else:
            print(f"   ❌ 空账户列表测试失败")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 聚合邮件API调试")
    print("=" * 60)
    
    # 测试单个账户
    working_account = test_single_account_detailed()
    
    if not working_account:
        print("\n❌ 单个账户测试失败，无法继续聚合测试")
        return
    
    # 测试聚合API
    aggregate_success = test_aggregate_api_step_by_step()
    
    # 测试空账户列表
    empty_list_success = test_empty_accounts_list()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 调试总结")
    print("=" * 60)
    
    print(f"   单个账户测试: {'✅ 成功' if working_account else '❌ 失败'}")
    print(f"   聚合API测试: {'✅ 成功' if aggregate_success else '❌ 失败'}")
    print(f"   空账户列表测试: {'✅ 成功' if empty_list_success else '❌ 失败'}")
    
    if working_account and aggregate_success:
        print("\n🎉 聚合API修复成功！")
        print("\n💡 建议:")
        print("   1. 重启前端服务")
        print("   2. 在浏览器中测试邮件获取功能")
        print("   3. 检查浏览器控制台是否有错误")
    else:
        print("\n⚠️ 仍有问题需要解决")
        print("\n🔧 下一步:")
        print("   1. 检查后端日志")
        print("   2. 验证账户token有效性")
        print("   3. 检查网络连接")

if __name__ == "__main__":
    main()
