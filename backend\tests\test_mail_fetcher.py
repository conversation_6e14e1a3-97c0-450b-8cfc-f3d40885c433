"""
邮件获取功能测试脚本
"""

from mail_fetcher import MailFetcher
from models import AccountDetail

def test_mail_fetcher():
    """测试邮件获取功能"""
    print("开始测试邮件获取功能...")
    
    # 创建测试账户（请替换为真实的凭证进行测试）
    test_account = AccountDetail(
        id=1,
        email_address="<EMAIL>",  # 替换为真实邮箱
        client_id="your-client-id",              # 替换为真实 Client ID
        refresh_token="your-refresh-token",      # 替换为真实 Refresh Token
        status="OK",
        last_updated="2024-01-01T00:00:00"
    )
    
    # 创建邮件获取器
    fetcher = MailFetcher()
    
    # 测试获取 access_token
    print("测试获取 access_token...")
    success, result = fetcher.get_access_token(test_account.client_id, test_account.refresh_token, test_account.email_address)
    
    if success:
        print(f"✓ 成功获取 access_token: {result[:20]}...")
        
        # 测试获取邮件
        print("测试获取邮件...")
        emails = fetcher.fetch_emails(test_account, folder='inbox', limit=3)
        
        if isinstance(emails, list):
            print(f"✓ 成功获取 {len(emails)} 封邮件")
            for i, email_info in enumerate(emails, 1):
                print(f"\n--- 邮件 {i} ---")
                print(f"主题: {email_info['subject']}")
                print(f"发件人: {email_info['mail_from']}")
                print(f"收件人: {email_info['mail_to']}")
                print(f"时间: {email_info['mail_dt']}")
                print(f"正文预览: {email_info['body'][:100]}...")
        else:
            print(f"✗ 获取邮件失败: {emails}")
    else:
        print(f"✗ 获取 access_token 失败: {result}")
    
    print("\n邮件获取功能测试完成！")

if __name__ == "__main__":
    print("注意：此测试需要真实的邮箱凭证才能运行")
    print("请在 test_account 中填入真实的邮箱地址、Client ID 和 Refresh Token")
    print("如果没有真实凭证，此测试将失败，这是正常的。")
    print()
    
    # 取消注释下面这行来运行测试（需要真实凭证）
    # test_mail_fetcher()
