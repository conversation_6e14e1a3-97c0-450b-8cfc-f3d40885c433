<script setup>
import { ref, onMounted } from 'vue'
import * as api from './services/api'
import AccountList from './components/AccountList.vue'
import EmailList from './components/EmailList.vue'
import EmailDetail from './components/EmailDetail.vue'
import ImportModal from './components/ImportModal.vue'
import EmailStats from './components/EmailStats.vue'
import FetchEmailModal from './components/FetchEmailModal.vue'

// 响应式数据
const accounts = ref([])
const emails = ref([])
const selectedAccounts = ref([])

const selectedEmail = ref(null)
const statusMessage = ref('📭 请选择账户，然后点击"获取邮件"配置获取条件')
const statusType = ref('info')
const isLoading = ref(false)
const isAccountsLoading = ref(false)
const isImportModalOpen = ref(false)
const isFetchModalOpen = ref(false)
const connectionStatus = ref(true)
const showStats = ref(false)

// 组件引用
const emailListRef = ref(null)

// 初始化应用
onMounted(async () => {
  await loadAccounts()
})

// 加载账户列表
const loadAccounts = async () => {
  try {
    isAccountsLoading.value = true
    updateStatus('🔄 正在加载账户...', 'info')
    accounts.value = await api.fetchAccounts()
    updateConnectionStatus(true)
    updateStatus('📭 请选择账户，然后点击"获取邮件"配置获取条件', 'info')
  } catch (error) {
    console.error('加载账户失败:', error)
    updateConnectionStatus(false)
    updateStatus(`❌ 初始化失败：${error.message}`, 'error')
  } finally {
    isAccountsLoading.value = false
  }
}

// 打开获取邮件弹窗
const openFetchModal = () => {
  if (selectedAccounts.value.length === 0) {
    updateStatus('⚠️ 请先选择要获取邮件的账户', 'warning')
    return
  }
  isFetchModalOpen.value = true
}

// 关闭获取邮件弹窗
const closeFetchModal = () => {
  isFetchModalOpen.value = false
}

// 获取邮件（使用弹窗配置）
const handleFetchEmails = async (config) => {
  if (isLoading.value) return
  
  try {
    // 设置加载状态
    isLoading.value = true
    updateStatus('🔄 正在获取邮件...', 'info')
    
    let emailData
    
    // 使用后端支持的最大值500，获取更多邮件
    const apiLimit = 500
    
    if (selectedAccounts.value.length === 1) {
      // 单个账户
      emailData = await api.fetchEmailsForAccount(selectedAccounts.value[0], config.folders, apiLimit)
    } else {
      // 多个账户 - 使用异步并发版本
      emailData = await api.fetchAggregatedEmailsAsync(selectedAccounts.value, config.folders, apiLimit)
    }
    
    // 获取邮件列表
    let emailList = emailData.messages || []
    
    // 应用日期过滤
    if (config.dateRange !== 'all') {
      const now = new Date()
      let startDate, endDate
      
      switch (config.dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          endDate = new Date(startDate)
          endDate.setDate(endDate.getDate() + 1)
          break
        case 'week':
          startDate = new Date(now)
          startDate.setDate(startDate.getDate() - 7)
          endDate = now
          break
        case 'month':
          startDate = new Date(now)
          startDate.setMonth(startDate.getMonth() - 1)
          endDate = now
          break
        case 'custom':
          startDate = new Date(config.startDate)
          endDate = new Date(config.endDate)
          endDate.setDate(endDate.getDate() + 1) // 包含结束日期
          break
      }
      
      if (startDate && endDate) {
        emailList = emailList.filter(email => {
          const emailDate = new Date(email.date)
          return emailDate >= startDate && emailDate < endDate
        })
      }
    }
    
    // 不再应用前端数量限制，获取所有符合条件的邮件
    
    // 更新邮件列表
    emails.value = emailList
    const folders = config.folders
    
    updateStatus(`📧 共找到 ${emailList.length} 封邮件 (来自 ${folders.join(', ')})`, 'success')
    
    // 清空详情面板
    selectedEmail.value = null
    emailListRef.value?.clearSelection()
    
  } catch (error) {
    console.error('获取邮件失败:', error)
    updateStatus(`❌ 获取失败：${error.message}`, 'error')
    emails.value = []
  } finally {
    isLoading.value = false
  }
}

// 清空邮件列表
const clearEmails = () => {
  emails.value = []
  selectedEmail.value = null
  emailListRef.value?.clearSelection()
  updateStatus('📭 请选择账户，然后点击"获取邮件"配置获取条件', 'info')
}

// 处理邮件选择
const handleEmailSelected = (email, index) => {
  selectedEmail.value = email
}

// 处理账户删除
const handleDeleteAccount = async (account) => {
  try {
    // 检查删除的账户是否正在被选中
    const wasSelected = selectedAccounts.value.includes(account.email_address)
    
    await api.deleteAccount(account.email_address)
    
    // 从选中列表中移除
    selectedAccounts.value = selectedAccounts.value.filter(email => email !== account.email_address)
    
    // 重新加载账户列表，确保数据同步
    await loadAccounts()
    
    updateStatus(`✅ 账户 "${account.email_address}" 已删除`, 'success')
    
    // 如果删除的账户之前被选中，清空邮件列表
    if (wasSelected) {
      clearEmails()
    }
  } catch (error) {
    console.error('删除账户失败:', error)
    updateStatus(`❌ 删除失败：${error.message}`, 'error')
    alert(`删除账户失败：${error.message}`)
  }
}

// 处理批量删除账户
const handleBatchDelete = async (accountsToDelete) => {
  if (accountsToDelete.length === 0) return
  
  updateStatus(`🔄 正在删除 ${accountsToDelete.length} 个账户...`, 'info')
  
  let successCount = 0
  let failureCount = 0
  const failures = []
  const deletedEmails = []
  
  try {
    for (const account of accountsToDelete) {
      try {
        await api.deleteAccount(account.email_address)
        successCount++
        deletedEmails.push(account.email_address)
        
      } catch (error) {
        failureCount++
        failures.push({
          email: account.email_address,
          error: error.message
        })
        console.error(`删除账户失败 ${account.email_address}:`, error)
      }
    }
    
    // 从选中列表中移除成功删除的账户
    selectedAccounts.value = selectedAccounts.value.filter(email => !deletedEmails.includes(email))
    
    // 重新加载账户列表，确保数据同步
    await loadAccounts()
    
    // 显示结果
    if (failureCount === 0) {
      updateStatus(`✅ 成功删除 ${successCount} 个账户`, 'success')
    } else {
      updateStatus(`⚠️ 删除完成：成功 ${successCount} 个，失败 ${failureCount} 个`, 'warning')
      
      if (failures.length > 0) {
        const failureList = failures.map(f => `${f.email}: ${f.error}`).join('\n')
        alert(`部分账户删除失败：\n\n${failureList}`)
      }
    }
    
    // 清空邮件列表
    clearEmails()
    
  } catch (error) {
    console.error('批量删除过程出错:', error)
    updateStatus(`❌ 批量删除失败：${error.message}`, 'error')
  }
}

// 处理清空所有账户
const handleClearAll = async (allAccounts) => {
  if (allAccounts.length === 0) return
  
  updateStatus(`🔄 正在清空所有 ${allAccounts.length} 个账户...`, 'info')
  
  let successCount = 0
  let failureCount = 0
  const failures = []
  
  try {
    for (const account of allAccounts) {
      try {
        await api.deleteAccount(account.email_address)
        successCount++
      } catch (error) {
        failureCount++
        failures.push({
          email: account.email_address,
          error: error.message
        })
        console.error(`删除账户失败 ${account.email_address}:`, error)
      }
    }
    
    // 刷新账户列表
    await loadAccounts()
    
    // 清空选中状态和邮件列表
    selectedAccounts.value = []
    clearEmails()
    
    // 显示结果
    if (failureCount === 0) {
      updateStatus(`✅ 成功清空所有账户 (${successCount} 个)`, 'success')
    } else {
      updateStatus(`⚠️ 清空完成：成功 ${successCount} 个，失败 ${failureCount} 个`, 'warning')
      
      if (failures.length > 0) {
        const failureList = failures.map(f => `${f.email}: ${f.error}`).join('\n')
        alert(`部分账户删除失败：\n\n${failureList}`)
      }
    }
    
  } catch (error) {
    console.error('清空账户过程出错:', error)
    updateStatus(`❌ 清空账户失败：${error.message}`, 'error')
  }
}

// 切换统计面板显示
const toggleStats = () => {
  showStats.value = !showStats.value
}

// 处理导入账户
const handleImportAccounts = async (accountsToImport, options) => {
  const totalAccounts = accountsToImport.length
  let successCount = 0
  let failureCount = 0
  const failures = []
  
  try {
    // 尝试批量导入
    if (totalAccounts > 1) {
      try {
        options.onProgress?.(0, totalAccounts, '准备批量导入...')
        const result = await api.addAccountsBatch(accountsToImport)
        
        if (result.success_count) {
          successCount = result.success_count
          failureCount = result.failure_count || 0
          
          if (result.failures) {
            failures.push(...result.failures)
          }
          
          options.onProgress?.(totalAccounts, totalAccounts, '批量导入完成')
        }
      } catch (batchError) {
        console.warn('批量导入失败，回退到逐个导入:', batchError)
        
        // 批量导入失败，回退到逐个导入
        for (let i = 0; i < totalAccounts; i++) {
          const account = accountsToImport[i]
          
          // 更新进度
          options.onProgress?.(i + 1, totalAccounts, account.email_address)
          
          try {
            await api.addAccount(account)
            successCount++
          } catch (error) {
            failureCount++
            failures.push({
              email: account.email_address,
              error: error.message
            })
            console.error(`导入账户失败 ${account.email_address}:`, error)
          }
          
          // 添加延迟避免请求过快
          await new Promise(resolve => setTimeout(resolve, 300))
        }
      }
    } else {
      // 单个账户直接导入
      const account = accountsToImport[0]
      options.onProgress?.(1, 1, account.email_address)
      
      try {
        await api.addAccount(account)
        successCount = 1
      } catch (error) {
        failureCount = 1
        failures.push({
          email: account.email_address,
          error: error.message
        })
        console.error(`导入账户失败 ${account.email_address}:`, error)
      }
    }
    
    // 刷新账户列表
    if (successCount > 0) {
      await loadAccounts()
      updateStatus(`✅ 成功导入 ${successCount} 个账户`, 'success')
    } else {
      updateStatus(`❌ 导入失败，没有成功导入任何账户`, 'error')
    }
    
    // 显示结果
    const message = `导入完成！\n成功: ${successCount} 个\n失败: ${failureCount} 个`
    if (failures.length > 0) {
      console.log('导入失败详情:', failures)
      const failureDetails = failures.map(f => `${f.email}: ${f.error}`).join('\n')
      alert(`${message}\n\n失败详情:\n${failureDetails}`)
    } else {
      alert(message)
    }
    
  } catch (error) {
    console.error('导入过程出错:', error)
    throw error
  }
}

// 更新状态消息
const updateStatus = (message, type = 'info') => {
  statusMessage.value = message
  statusType.value = type
}

// 更新连接状态
const updateConnectionStatus = (isConnected) => {
  connectionStatus.value = isConnected
}

// 打开导入模态框
const openImportModal = () => {
  isImportModalOpen.value = true
}

// 关闭导入模态框
const closeImportModal = () => {
  isImportModalOpen.value = false
}
</script>

<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <header class="app-header">
      <h1 class="app-title">📧 多账户邮件聚合管理系统</h1>
      
      <!-- 顶部操作按钮区 -->
      <div class="header-actions">
        <button 
          type="button" 
          class="btn btn-primary btn-header" 
          :disabled="isLoading"
          @click="openFetchModal"
        >
          <span class="btn-icon">{{ isLoading ? '🔄' : '📧' }}</span>
          <span class="btn-text">{{ isLoading ? '获取中...' : '获取邮件' }}</span>
        </button>
        
        <button
          type="button"
          class="btn btn-secondary btn-header"
          @click="clearEmails"
        >
          <span class="btn-icon">🗑️</span>
          <span class="btn-text">清空列表</span>
        </button>

        <button
          type="button"
          class="btn btn-import btn-header"
          @click="openImportModal"
        >
          <span class="btn-icon">📥</span>
          <span class="btn-text">导入邮箱</span>
        </button>

        <button
          type="button"
          :class="['btn', 'btn-header', showStats ? 'btn-primary' : 'btn-secondary']"
          @click="toggleStats"
        >
          <span class="btn-icon">📊</span>
          <span class="btn-text">{{ showStats ? '隐藏统计' : '显示统计' }}</span>
        </button>
      </div>

      <div class="app-status">
        <span class="status-indicator">{{ connectionStatus ? '🟢' : '🔴' }}</span>
        <span class="status-text">{{ connectionStatus ? '服务正常' : '服务异常' }}</span>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main">
      <!-- 左侧控制面板 -->
      <aside class="control-panel">
        <!-- 账户选择区 -->
        <AccountList
          :accounts="accounts"
          :loading="isAccountsLoading"
          v-model="selectedAccounts"
          @delete-account="handleDeleteAccount"
          @batch-delete="handleBatchDelete"
          @clear-all="handleClearAll"
        />


      </aside>

      <!-- 中间邮件列表区域 -->
      <EmailList
        ref="emailListRef"
        :emails="emails"
        :loading="isLoading"
        :status-message="statusMessage"
        :status-type="statusType"
        @email-selected="handleEmailSelected"
      />

      <!-- 右侧区域 -->
      <div class="right-panel">
        <!-- 统计面板（可选显示） -->
        <EmailStats 
          v-if="showStats"
          :emails="emails"
          :accounts="accounts"
          @refresh="loadAccounts"
          class="stats-panel"
        />
        
        <!-- 邮件详情区域 -->
        <EmailDetail 
          :selected-email="selectedEmail" 
          :class="['email-detail-panel', { 'with-stats': showStats }]"
        />
      </div>
    </main>

    <!-- 页面底部 -->
    <footer class="app-footer">
      <div class="footer-content">
        <span>多账户邮件聚合管理系统 v2.0.0</span>
        <span class="footer-separator">|</span>
        <span>API 服务: <span>http://localhost:8000</span></span>
      </div>
    </footer>

    <!-- 导入邮箱模态框 -->
    <ImportModal
      :is-open="isImportModalOpen"
      :existing-accounts="accounts"
      @close="closeImportModal"
      @import-accounts="handleImportAccounts"
    />

    <!-- 获取邮件配置弹窗 -->
    <FetchEmailModal
      :is-open="isFetchModalOpen"
      :selected-accounts="selectedAccounts"
      @close="closeFetchModal"
      @fetch-emails="handleFetchEmails"
    />
  </div>
</template>

<style>
/* ===== 全局样式重置和基础设置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 颜色变量 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* 间距变量 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* 圆角变量 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    
    /* 阴影变量 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* 过渡变量 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
    font-size: 14px;
}

/* 无障碍辅助类 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== 主布局容器 ===== */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.app-header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-sm);
    min-height: 70px;
    gap: var(--spacing-lg);
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    flex-shrink: 0;
}

/* ===== 头部操作按钮区域 ===== */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    justify-content: center;
    max-width: 600px;
}

.btn-header {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    font-weight: 500;
    min-height: 40px;
    white-space: nowrap;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-header .btn-icon {
    font-size: 0.875rem;
}

.btn-header .btn-text {
    display: none;
}

/* 在较大屏幕上显示按钮文字 */
@media (min-width: 1200px) {
    .btn-header .btn-text {
        display: inline;
    }
    
    .header-actions {
        gap: var(--spacing-md);
    }
    
    .btn-header {
        padding: var(--spacing-sm) var(--spacing-lg);
    }
}

.app-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.status-indicator {
    font-size: 0.75rem;
}

.status-text {
    color: var(--text-secondary);
}

/* ===== 主内容区域 - 三栏布局 ===== */
.app-main {
  flex: 1;
  display: grid;
  grid-template-columns: 280px 450px 1fr;
  gap: 0;
  min-height: 0;
}

/* ===== 右侧面板 ===== */
.right-panel {
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;
}

.stats-panel {
  flex-shrink: 0;
  max-height: 300px;
  border-bottom: 1px solid var(--border-color);
}

.email-detail-panel {
  flex: 1;
  overflow: hidden;
}

.email-detail-panel.with-stats {
  border-top: 1px solid var(--border-color);
}

/* ===== 左侧控制面板 ===== */
.control-panel {
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}



.panel-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.account-count {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: normal;
}

/* ===== 账户选择区域 ===== */
.accounts-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.account-item {
    display: flex;
    align-items: center;
}

.all-accounts {
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-sm);
}

.accounts-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    max-height: 200px;
    overflow-y: auto;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== 复选框样式 ===== */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
    user-select: none;
}

.checkbox-label:hover {
    background-color: #f1f5f9;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkbox-custom {
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
    font-weight: bold;
}

.checkbox-label input[type="checkbox"]:focus + .checkbox-custom {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.account-text,
.folder-text {
    font-size: 0.875rem;
    color: var(--text-primary);
}

.account-text strong {
    font-weight: 600;
}

/* ===== 账户删除按钮样式 ===== */
.account-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.account-item:hover {
    background-color: #f8fafc;
}

.account-item:hover .delete-account-btn {
    opacity: 1;
}

.delete-account-btn {
    background: none;
    border: none;
    color: var(--error-color);
    font-size: 0.875rem;
    font-weight: bold;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    opacity: 0;
    transition: all var(--transition-fast);
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.delete-account-btn:hover {
    background-color: #fee2e2;
    color: #dc2626;
    transform: scale(1.1);
}

.delete-account-btn:active {
    transform: scale(0.95);
}

.delete-account-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.delete-account-btn:disabled:hover {
    background-color: transparent;
    color: var(--error-color);
}

/* ===== 文件夹选择区域 ===== */
.folders-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.folder-item {
    display: flex;
    align-items: center;
}

/* ===== 操作按钮区域 ===== */
.actions-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    min-height: 40px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-import {
    background-color: var(--success-color);
    color: white;
}

.btn-import:hover:not(:disabled) {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
    min-height: 32px;
}

.btn-icon {
    font-size: 1rem;
}

.btn-text {
    font-size: 0.875rem;
}

/* ===== 中间邮件列表区域 ===== */
.email-list-panel {
    background: var(--surface-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.status-bar {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--surface-color);
}

.status-info {
    margin-top: var(--spacing-sm);
}

.status-message {
    font-size: 0.875rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.email-list-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.email-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

/* ===== 空状态样式 ===== */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-muted);
    min-height: 200px;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.empty-description {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* ===== 邮件项样式 ===== */
.email-item {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--surface-color);
}

.email-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.email-item.selected {
    border-color: var(--primary-color);
    background-color: #eff6ff;
    box-shadow: var(--shadow-md);
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.email-subject {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.4;
    flex: 1;
    margin-right: var(--spacing-sm);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.email-date {
    font-size: 0.75rem;
    color: var(--text-muted);
    white-space: nowrap;
}

.email-from {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.email-badges {
    display: flex;
    gap: var(--spacing-xs);
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
}

.badge-account {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-folder {
    background-color: #f3e8ff;
    color: #7c3aed;
}

/* ===== 右侧邮件详情区域 ===== */
.email-detail-panel {
    background: var(--surface-color);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.detail-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.detail-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* ===== 详情空状态 ===== */
.detail-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-muted);
    flex: 1;
}

.detail-empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.detail-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.detail-empty-description {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* ===== 邮件详情内容 ===== */
.detail-mail {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.mail-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: #f8fafc;
}

.mail-subject {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
    word-wrap: break-word;
}

.mail-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.mail-meta-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.meta-label {
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 60px;
    flex-shrink: 0;
}

.meta-value {
    color: var(--text-primary);
    word-wrap: break-word;
    flex: 1;
}

.mail-body {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.mail-content {
    line-height: 1.6;
    color: var(--text-primary);
    word-wrap: break-word;
}

.mail-content img {
    max-width: 100%;
    height: auto;
}

.mail-content a {
    color: var(--primary-color);
    text-decoration: underline;
}

.mail-content a:hover {
    color: var(--primary-hover);
}

/* ===== 页面底部 ===== */
.app-footer {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-xl);
    text-align: center;
}

.footer-content {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-separator {
    color: var(--border-color);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1400px) {
  .app-main {
    grid-template-columns: 260px 400px 1fr;
  }
}

@media (max-width: 1024px) {
  .app-main {
    grid-template-columns: 240px 350px 1fr;
  }
}

@media (max-width: 768px) {
    .app-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .control-panel {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        max-height: 300px;
    }
    
    .email-detail-panel {
        border-left: none;
        border-top: 1px solid var(--border-color);
        max-height: 400px;
    }
    
    .app-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .app-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .app-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .control-panel,
    .email-detail-panel {
        padding: var(--spacing-md);
    }
    
    .actions-container {
        flex-direction: row;
    }
    
    .btn {
        flex: 1;
        min-height: 44px;
    }
}

/* ===== 导入功能样式 ===== */

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--spacing-md);
}

.modal-container {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background-color: #f1f5f9;
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 标签页样式 */
.import-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.tab-button {
    background: none;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--text-secondary);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tab-button:hover {
    color: var(--text-primary);
    background-color: #f8fafc;
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: #eff6ff;
}

.tab-icon {
    font-size: 1rem;
}

/* 标签内容样式 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.import-section {
    margin-bottom: var(--spacing-lg);
}

.section-subtitle {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* 文件上传样式 */
.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-md);
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: #f8fafc;
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: #eff6ff;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.upload-icon {
    font-size: 3rem;
    color: var(--text-muted);
}

.upload-text {
    font-size: 1rem;
    color: var(--text-primary);
}

.upload-hint {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* 格式信息样式 */
.format-info {
    background-color: #f8fafc;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

.format-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.format-examples {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.format-item {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.format-item code {
    background-color: #e2e8f0;
    padding: 2px 4px;
    border-radius: var(--radius-sm);
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
}

/* 表单样式 */
.manual-form {
    background-color: #f8fafc;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
    min-height: 80px;
    resize: vertical;
}

/* 手动列表样式 */
.manual-list {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
}

.manual-list h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.manual-accounts {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.empty-list {
    color: var(--text-muted);
    font-size: 0.875rem;
    text-align: center;
    padding: var(--spacing-md);
}

.manual-account-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background-color: #f8fafc;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
}

.account-info {
    flex: 1;
}

.account-email {
    font-weight: 600;
    color: var(--text-primary);
}

.account-client {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.remove-account {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.remove-account:hover {
    background-color: #fee2e2;
}

/* 剪贴板样式 */
.clipboard-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.clipboard-textarea {
    width: 100%;
    min-height: 200px;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-family: 'Courier New', monospace;
    color: var(--text-primary);
    resize: vertical;
    transition: border-color var(--transition-fast);
}

.clipboard-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 预览样式 */
.preview-section {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.preview-stats {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    font-size: 0.875rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: #f8fafc;
    border-radius: var(--radius-sm);
}

.stat-valid {
    color: var(--success-color);
}

.stat-invalid {
    color: var(--error-color);
}

.stat-duplicate {
    color: var(--warning-color);
}

.preview-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.preview-item:last-child {
    border-bottom: none;
}

.preview-item.valid {
    background-color: #f0fdf4;
}

.preview-item.invalid {
    background-color: #fef2f2;
}

.preview-item.duplicate {
    background-color: #fffbeb;
}

.preview-status {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.status-valid {
    background-color: var(--success-color);
    color: white;
}

.status-invalid {
    background-color: var(--error-color);
    color: white;
}

.status-duplicate {
    background-color: var(--warning-color);
    color: white;
}

/* 进度条样式 */
.progress-section {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e2e8f0;
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.progress-details {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-container {
        max-width: 95vw;
        margin: var(--spacing-sm);
    }

    .import-tabs {
        flex-direction: column;
    }

    .tab-button {
        justify-content: center;
        padding: var(--spacing-sm);
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}
</style>