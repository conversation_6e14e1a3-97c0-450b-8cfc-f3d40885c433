# 使用Python 3.11官方镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据和日志目录
RUN mkdir -p /app/data /app/logs

# 设置环境变量
ENV FLASK_ENV=production
ENV DATABASE_PATH=/app/data/accounts.db
ENV LOG_FILE=/app/logs/app.log
ENV PORT=8000

# 暴露端口
EXPOSE 8000

# 创建非root用户
RUN useradd -r -s /bin/false appuser && \
    chown -R appuser:appuser /app

USER appuser

# 启动命令
CMD ["python", "start_production.py"]
