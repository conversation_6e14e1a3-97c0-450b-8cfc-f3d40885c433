"""
测试清空列表功能
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_clear_list_function():
    """测试清空列表功能"""
    print("🧪 测试清空列表功能...")
    
    try:
        # 配置 Chrome 选项
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 打开前端页面
        frontend_url = "http://localhost:3000"
        driver.get(frontend_url)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 检查清空列表按钮是否存在
        try:
            clear_btn = wait.until(EC.presence_of_element_located((By.ID, "clearListBtn")))
            print("   ✅ 清空列表按钮找到")
            print(f"   按钮文本: {clear_btn.text}")
            print(f"   按钮可见: {clear_btn.is_displayed()}")
            print(f"   按钮可点击: {clear_btn.is_enabled()}")
        except Exception as e:
            print(f"   ❌ 清空列表按钮未找到: {e}")
            driver.quit()
            return False
        
        # 检查邮件列表元素
        try:
            email_list = driver.find_element(By.ID, "emailList")
            print("   ✅ 邮件列表元素找到")
            print(f"   初始内容: {email_list.get_attribute('innerHTML')[:100]}...")
        except Exception as e:
            print(f"   ❌ 邮件列表元素未找到: {e}")
            driver.quit()
            return False
        
        # 检查详情面板元素
        try:
            detail_empty = driver.find_element(By.ID, "detailEmpty")
            detail_mail = driver.find_element(By.ID, "detailMail")
            print("   ✅ 详情面板元素找到")
            print(f"   detailEmpty 可见: {detail_empty.is_displayed()}")
            print(f"   detailMail 可见: {detail_mail.is_displayed()}")
        except Exception as e:
            print(f"   ❌ 详情面板元素未找到: {e}")
            driver.quit()
            return False
        
        # 模拟添加一些邮件内容
        print("   📧 模拟添加邮件内容...")
        driver.execute_script("""
            const emailList = document.getElementById('emailList');
            emailList.innerHTML = `
                <div class="email-item">
                    <div class="email-subject">测试邮件 1</div>
                    <div class="email-from"><EMAIL></div>
                </div>
                <div class="email-item">
                    <div class="email-subject">测试邮件 2</div>
                    <div class="email-from"><EMAIL></div>
                </div>
            `;
            
            // 显示详情
            document.getElementById('detailEmpty').style.display = 'none';
            document.getElementById('detailMail').style.display = 'block';
        """)
        
        time.sleep(1)
        
        # 检查内容是否添加成功
        email_items = driver.find_elements(By.CLASS_NAME, "email-item")
        print(f"   ✅ 添加了 {len(email_items)} 个邮件项")
        
        # 点击清空列表按钮
        print("   🗑️ 点击清空列表按钮...")
        clear_btn.click()
        
        time.sleep(2)
        
        # 检查是否清空成功
        email_list_after = driver.find_element(By.ID, "emailList")
        detail_empty_after = driver.find_element(By.ID, "detailEmpty")
        detail_mail_after = driver.find_element(By.ID, "detailMail")
        
        # 检查邮件列表是否恢复空状态
        empty_states = driver.find_elements(By.CLASS_NAME, "empty-state")
        email_items_after = driver.find_elements(By.CLASS_NAME, "email-item")
        
        print(f"   📊 清空后状态:")
        print(f"      空状态元素数量: {len(empty_states)}")
        print(f"      邮件项数量: {len(email_items_after)}")
        print(f"      detailEmpty 可见: {detail_empty_after.is_displayed()}")
        print(f"      detailMail 可见: {detail_mail_after.is_displayed()}")
        
        # 判断清空是否成功
        success = (
            len(empty_states) > 0 and 
            len(email_items_after) == 0 and
            detail_empty_after.is_displayed() and
            not detail_mail_after.is_displayed()
        )
        
        if success:
            print("   ✅ 清空列表功能正常工作")
        else:
            print("   ❌ 清空列表功能有问题")
            
            # 输出调试信息
            print("   🔍 调试信息:")
            print(f"      邮件列表内容: {email_list_after.get_attribute('innerHTML')[:200]}...")
        
        driver.quit()
        return success
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def main():
    """主测试函数"""
    print("🚀 清空列表功能测试")
    print("=" * 50)
    
    # 检查服务状态
    print("1. 检查前端服务...")
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端服务正常")
        else:
            print(f"   ❌ 前端服务异常: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 前端服务连接失败: {e}")
        return
    
    # 测试清空功能
    print("\n2. 测试清空列表功能...")
    success = test_clear_list_function()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 清空列表功能测试通过")
    else:
        print("⚠️ 清空列表功能需要修复")
        print("\n🔧 可能的问题:")
        print("   1. JavaScript 事件监听器未正确绑定")
        print("   2. DOM 元素引用错误")
        print("   3. 函数逻辑有误")
        print("   4. CSS 样式影响显示")

if __name__ == "__main__":
    main()
