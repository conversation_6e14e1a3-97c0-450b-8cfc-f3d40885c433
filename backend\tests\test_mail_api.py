"""
邮件获取 API 测试脚本
"""

import requests
import json
import time

# API 基础 URL
BASE_URL = "http://localhost:8000"

def test_mail_api():
    """测试邮件获取 API"""
    print("开始测试邮件获取 API...")
    print(f"API 服务地址: {BASE_URL}")
    print("=" * 60)
    
    # 获取所有账户
    print("1. 获取所有可用账户...")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts", timeout=10)
        if response.status_code == 200:
            accounts = response.json()
            print(f"   找到 {len(accounts)} 个账户")
            
            # 过滤掉测试账户
            real_accounts = [acc for acc in accounts if not acc['email_address'].startswith('invalid')]
            print(f"   其中 {len(real_accounts)} 个真实账户")
            
            if not real_accounts:
                print("   没有真实账户可供测试")
                return
                
        else:
            print(f"   获取账户失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    print("\n2. 测试邮件获取功能...")
    
    # 测试前3个账户
    test_accounts = real_accounts[:3]
    
    for i, account in enumerate(test_accounts, 1):
        email_address = account['email_address']
        print(f"\n   {i}. 测试账户: {email_address}")
        
        # 测试获取收件箱邮件 (向后兼容测试)
        print(f"      获取收件箱邮件 (limit=3, 向后兼容模式)...")
        try:
            response = requests.get(
                f"{BASE_URL}/api/mail/{email_address}",
                params={"folder": "inbox", "limit": 3},
                timeout=60  # 邮件获取可能需要较长时间
            )
            
            print(f"      状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"      ✓ 成功获取 {result['total_count']} 封邮件")
                print(f"      ✓ 文件夹: {result.get('folders', result.get('folder', 'unknown'))}")

                # 显示邮件摘要
                for j, message in enumerate(result['messages'], 1):
                    folder_info = f" [来自: {message.get('folder', 'unknown')}]" if 'folder' in message else ""
                    print(f"         邮件 {j}: {message['subject'][:50]}...{folder_info}")
                    print(f"         发件人: {message['sender'][:30]}...")
                    print(f"         时间: {message['date']}")
                    
            elif response.status_code == 401:
                result = response.json()
                print(f"      ⚠ Token 失效: {result['message']}")
                
            elif response.status_code == 403:
                result = response.json()
                print(f"      ⚠ 登录失败: {result['message']}")
                
            elif response.status_code == 404:
                result = response.json()
                print(f"      ✗ 账户不存在: {result['message']}")
                
            else:
                result = response.json()
                print(f"      ✗ 失败: {result.get('message', '未知错误')}")
                
        except requests.exceptions.Timeout:
            print(f"      ✗ 请求超时 (60秒)")
        except requests.exceptions.ConnectionError:
            print(f"      ✗ 连接失败")
        except Exception as e:
            print(f"      ✗ 错误: {e}")
        
        # 测试多文件夹功能
        print(f"      测试多文件夹功能 (inbox,junk)...")
        try:
            response = requests.get(
                f"{BASE_URL}/api/mail/{email_address}",
                params={"folders": "inbox,junk", "limit": 2},
                timeout=60
            )

            print(f"      状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"      ✓ 多文件夹成功获取 {result['total_count']} 封邮件")
                print(f"      ✓ 文件夹: {result['folders']}")

                # 统计各文件夹邮件数量
                folder_counts = {}
                for msg in result['messages']:
                    folder = msg.get('folder', 'unknown')
                    folder_counts[folder] = folder_counts.get(folder, 0) + 1
                print(f"      ✓ 邮件分布: {folder_counts}")

            else:
                result = response.json()
                print(f"      ⚠ 多文件夹测试: {result.get('message', '未知错误')}")

        except Exception as e:
            print(f"      ✗ 多文件夹测试失败: {e}")

        # 在测试下一个账户前稍作等待
        if i < len(test_accounts):
            print(f"      等待 2 秒后测试下一个账户...")
            time.sleep(2)
    
    print("\n3. 测试不同文件夹...")
    
    # 使用第一个账户测试不同文件夹
    if test_accounts:
        test_email = test_accounts[0]['email_address']
        folders_to_test = ['inbox', 'junk', 'sent']
        
        for folder in folders_to_test:
            print(f"\n   测试文件夹 '{folder}' - {test_email}")
            try:
                response = requests.get(
                    f"{BASE_URL}/api/mail/{test_email}",
                    params={"folder": folder, "limit": 2},
                    timeout=30
                )
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✓ {folder} 文件夹: {result['total_count']} 封邮件")
                else:
                    result = response.json()
                    print(f"   ⚠ {folder} 文件夹: {result.get('message', '获取失败')}")
                    
            except Exception as e:
                print(f"   ✗ {folder} 文件夹测试失败: {e}")
    
    print("\n4. 测试参数验证...")
    
    # 测试无效参数
    if test_accounts:
        test_email = test_accounts[0]['email_address']
        
        # 测试无效的 limit
        print(f"\n   测试无效 limit 参数...")
        try:
            response = requests.get(
                f"{BASE_URL}/api/mail/{test_email}",
                params={"limit": 100},  # 超过最大限制
                timeout=10
            )
            print(f"   状态码: {response.status_code}")
            if response.status_code == 400:
                result = response.json()
                print(f"   ✓ 正确拒绝无效参数: {result['message']}")
            else:
                print(f"   ⚠ 未正确验证参数")
        except Exception as e:
            print(f"   ✗ 参数验证测试失败: {e}")
        
        # 测试不存在的账户
        print(f"\n   测试不存在的账户...")
        try:
            response = requests.get(
                f"{BASE_URL}/api/mail/<EMAIL>",
                params={"limit": 3},
                timeout=10
            )
            print(f"   状态码: {response.status_code}")
            if response.status_code == 404:
                result = response.json()
                print(f"   ✓ 正确处理不存在账户: {result['message']}")
            else:
                print(f"   ⚠ 未正确处理不存在账户")
        except Exception as e:
            print(f"   ✗ 不存在账户测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("邮件获取 API 测试完成！")


if __name__ == "__main__":
    test_mail_api()
