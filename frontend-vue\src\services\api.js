// ===== 全局配置和常量 =====
// 支持环境变量配置API地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
const EMAIL_LIMIT = 500;

// 调试信息
console.log('API Base URL:', API_BASE_URL);

// ===== API 通信模块 =====
/**
 * 通用 API 请求函数
 * @param {string} endpoint - API 端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API 响应数据
 */
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        return data;
    } catch (error) {
        console.error('API 请求失败:', error);
        throw error;
    }
}

/**
 * 获取所有账户
 * @returns {Promise<Array>} 账户列表
 */
export async function fetchAccounts() {
    return await apiRequest('/api/accounts');
}

/**
 * 获取单个账户的邮件
 * @param {string} email - 邮箱地址
 * @param {Array<string>} folders - 文件夹列表
 * @param {number} limit - 邮件数量限制
 * @returns {Promise<Object>} 邮件数据
 */
export async function fetchEmailsForAccount(email, folders, limit = EMAIL_LIMIT) {
    const foldersParam = folders.join(',');
    return await apiRequest(`/api/mail/${encodeURIComponent(email)}?folders=${foldersParam}&limit=${limit}`);
}

/**
 * 聚合获取多个账户的邮件 (同步版本)
 * @param {Array<string>} accounts - 账户列表
 * @param {Array<string>} folders - 文件夹列表
 * @param {number} limit - 每个文件夹的邮件数量限制
 * @returns {Promise<Object>} 聚合邮件数据
 */
export async function fetchAggregatedEmails(accounts, folders, limit = EMAIL_LIMIT) {
    return await apiRequest('/api/mail/aggregate', {
        method: 'POST',
        body: JSON.stringify({
            accounts: accounts,
            folders: folders,
            limit: limit
        })
    });
}

/**
 * 异步聚合获取多个账户的邮件 (并发优化版本)
 * @param {Array<string>} accounts - 账户列表
 * @param {Array<string>} folders - 文件夹列表
 * @param {number} limit - 每个文件夹的邮件数量限制
 * @returns {Promise<Object>} 聚合邮件数据
 */
export async function fetchAggregatedEmailsAsync(accounts, folders, limit = EMAIL_LIMIT) {
    return await apiRequest('/api/mail/aggregate-async', {
        method: 'POST',
        body: JSON.stringify({
            accounts: accounts,
            folders: folders,
            limit: limit
        })
    });
}

/**
 * 添加单个账户
 * @param {Object} accountData - 账户数据
 * @returns {Promise<Object>} 添加结果
 */
export async function addAccount(accountData) {
    return await apiRequest('/api/accounts', {
        method: 'POST',
        body: JSON.stringify(accountData)
    });
}

/**
 * 批量添加账户
 * @param {Array<Object>} accountsData - 账户数据列表
 * @returns {Promise<Object>} 批量添加结果
 */
export async function addAccountsBatch(accountsData) {
    return await apiRequest('/api/accounts/batch', {
        method: 'POST',
        body: JSON.stringify({
            accounts: accountsData
        })
    });
}

/**
 * 删除账户
 * @param {string} emailAddress - 邮箱地址
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteAccount(emailAddress) {
    return await apiRequest(`/api/accounts/${encodeURIComponent(emailAddress)}`, {
        method: 'DELETE'
    });
}
