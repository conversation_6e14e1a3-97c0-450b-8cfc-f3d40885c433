"""
多文件夹邮件聚合功能 - 使用示例
演示如何使用新的多文件夹功能
"""

import requests
import json
from datetime import datetime

# API 基础 URL
BASE_URL = "http://localhost:8000"

def demo_new_features():
    """演示新功能的使用方法"""
    print("🚀 多文件夹邮件聚合功能演示")
    print("=" * 60)
    
    # 获取一个测试账户
    print("1. 获取可用账户...")
    try:
        response = requests.get(f"{BASE_URL}/api/accounts")
        if response.status_code == 200:
            accounts = response.json()
            if accounts:
                test_email = accounts[0]['email_address']
                print(f"   使用账户: {test_email}")
            else:
                print("   没有可用账户，请先添加账户")
                return
        else:
            print("   获取账户失败")
            return
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    print("\n" + "=" * 60)
    print("2. 演示向后兼容性 - 原有单文件夹方式")
    
    # 原有的单文件夹方式
    print("\n   📁 使用原有的 folder 参数...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folder": "inbox", "limit": 3}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 成功获取 {result['total_count']} 封邮件")
            print(f"   📂 文件夹: {result['folders']}")
            
            for i, msg in enumerate(result['messages'][:2], 1):
                print(f"      邮件 {i}: {msg['subject'][:40]}... [来自: {msg['folder']}]")
        else:
            print(f"   ❌ 失败: {response.json()}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    print("\n" + "=" * 60)
    print("3. 演示新功能 - 多文件夹聚合")
    
    # 新的多文件夹方式 - GET 接口
    print("\n   📁📁 使用新的 folders 参数 (GET 方式)...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": "inbox,junk,sent", "limit": 2}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 成功聚合获取 {result['total_count']} 封邮件")
            print(f"   📂 请求的文件夹: inbox, junk, sent")
            print(f"   📂 返回的文件夹: {result['folders']}")
            
            # 统计各文件夹邮件数量
            folder_counts = {}
            for msg in result['messages']:
                folder = msg['folder']
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            print(f"   📊 邮件分布: {folder_counts}")
            
            print("   📧 邮件列表 (按时间倒序):")
            for i, msg in enumerate(result['messages'], 1):
                print(f"      {i}. [{msg['folder']}] {msg['subject'][:35]}...")
                print(f"         时间: {msg['date']}, 发件人: {msg['sender'][:25]}...")
                
        else:
            print(f"   ❌ 失败: {response.json()}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    # 新的聚合接口 - POST 方式
    print("\n   📁📁📁 使用新的聚合接口 (POST 方式)...")
    try:
        aggregate_request = {
            "email_address": test_email,
            "folders": ["inbox", "junk", "sent", "drafts"],
            "limit": 3
        }
        
        response = requests.post(
            f"{BASE_URL}/api/mail/aggregate",
            json=aggregate_request,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 聚合接口成功获取 {result['total_count']} 封邮件")
            print(f"   📂 请求的文件夹: {aggregate_request['folders']}")
            print(f"   📂 返回的文件夹: {result['folders']}")
            
            # 统计各文件夹邮件数量
            folder_counts = {}
            for msg in result['messages']:
                folder = msg['folder']
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            print(f"   📊 邮件分布: {folder_counts}")
            
            # 显示时间排序效果
            if len(result['messages']) > 1:
                first_date = result['messages'][0]['date']
                last_date = result['messages'][-1]['date']
                print(f"   ⏰ 时间范围: {first_date} ~ {last_date}")
                
        else:
            print(f"   ❌ 聚合失败: {response.json()}")
            
    except Exception as e:
        print(f"   ❌ 聚合错误: {e}")
    
    print("\n" + "=" * 60)
    print("4. 演示错误处理")
    
    # 测试部分无效文件夹
    print("\n   🔧 测试部分无效文件夹处理...")
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": "inbox,invalid_folder,junk", "limit": 2}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 部分成功: 获取 {result['total_count']} 封邮件")
            print(f"   📂 请求的文件夹: inbox, invalid_folder, junk")
            print(f"   📂 实际处理的文件夹: {result['folders']}")
            
            folder_counts = {}
            for msg in result['messages']:
                folder = msg['folder']
                folder_counts[folder] = folder_counts.get(folder, 0) + 1
            
            print(f"   📊 实际邮件分布: {folder_counts}")
            print("   ✅ 无效文件夹被忽略，不影响其他文件夹的处理")
            
        else:
            print(f"   ❌ 失败: {response.json()}")
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
    
    print("\n" + "=" * 60)
    print("5. 性能对比演示")
    
    # 对比单次多文件夹 vs 多次单文件夹
    print("\n   ⚡ 性能对比: 单次多文件夹 vs 多次单文件夹")
    
    folders_to_test = ["inbox", "junk", "sent"]
    
    # 方式1: 单次多文件夹请求
    print("   方式1: 单次多文件夹请求")
    start_time = datetime.now()
    try:
        response = requests.get(
            f"{BASE_URL}/api/mail/{test_email}",
            params={"folders": ",".join(folders_to_test), "limit": 2}
        )
        
        end_time = datetime.now()
        duration1 = (end_time - start_time).total_seconds()
        
        if response.status_code == 200:
            result = response.json()
            print(f"      ✅ 耗时: {duration1:.2f}秒, 获取: {result['total_count']} 封邮件")
        else:
            print(f"      ❌ 失败")
            
    except Exception as e:
        print(f"      ❌ 错误: {e}")
    
    # 方式2: 多次单文件夹请求
    print("   方式2: 多次单文件夹请求")
    start_time = datetime.now()
    total_emails = 0
    
    try:
        for folder in folders_to_test:
            response = requests.get(
                f"{BASE_URL}/api/mail/{test_email}",
                params={"folder": folder, "limit": 2}
            )
            
            if response.status_code == 200:
                result = response.json()
                total_emails += result['total_count']
        
        end_time = datetime.now()
        duration2 = (end_time - start_time).total_seconds()
        
        print(f"      ✅ 耗时: {duration2:.2f}秒, 获取: {total_emails} 封邮件")
        
        if duration1 < duration2:
            improvement = ((duration2 - duration1) / duration2) * 100
            print(f"      🚀 多文件夹方式提升性能 {improvement:.1f}%")
        
    except Exception as e:
        print(f"      ❌ 错误: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 新功能演示完成！")
    print("\n📋 总结:")
    print("   ✅ 向后兼容: 原有 API 调用方式完全兼容")
    print("   ✅ 多文件夹: 支持一次请求获取多个文件夹邮件")
    print("   ✅ 自动排序: 按时间倒序自动排列所有邮件")
    print("   ✅ 文件夹标识: 每封邮件包含来源文件夹信息")
    print("   ✅ 错误处理: 部分文件夹失败不影响整体结果")
    print("   ✅ 性能提升: 减少网络请求次数")


if __name__ == "__main__":
    demo_new_features()
