"""
Token管理器 - 处理refresh token的生命周期管理
"""

import requests
import logging
from datetime import datetime, timedelta
from database import get_all_accounts, update_account_tokens

logger = logging.getLogger(__name__)

class TokenManager:
    """Token生命周期管理器"""
    
    # Token过期策略
    TOKEN_WARNING_DAYS = 60  # 60天后警告
    TOKEN_REFRESH_DAYS = 75  # 75天后强制刷新
    TOKEN_MAX_AGE_DAYS = 90  # 90天后认为过期
    
    @classmethod
    def check_token_health(cls, account):
        """检查token健康状态"""
        if not account.last_updated:
            return "unknown"
            
        # 计算token年龄
        last_update = datetime.fromisoformat(account.last_updated.replace('Z', '+00:00'))
        age_days = (datetime.now() - last_update).days
        
        if age_days > cls.TOKEN_MAX_AGE_DAYS:
            return "expired"
        elif age_days > cls.TOKEN_REFRESH_DAYS:
            return "needs_refresh"
        elif age_days > cls.TOKEN_WARNING_DAYS:
            return "warning"
        else:
            return "healthy"
    
    @classmethod
    def refresh_token(cls, account):
        """刷新单个账户的token"""
        logger.info(f"尝试刷新token: {account.email_address}")
        
        # 确定正确的端点
        domain = account.email_address.split('@')[1].lower()
        if domain in ['hotmail.com', 'live.com']:
            token_url = "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
        else:
            token_url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
        
        data = {
            'client_id': account.client_id,
            'grant_type': 'refresh_token',
            'refresh_token': account.refresh_token,
        }
        
        try:
            response = requests.post(token_url, data=data, timeout=30)
            
            if response.status_code == 200:
                token_data = response.json()
                
                # 更新token
                new_access_token = token_data.get('access_token')
                new_refresh_token = token_data.get('refresh_token', account.refresh_token)
                
                # 保存新token
                update_account_tokens(
                    account.email_address,
                    new_access_token,
                    new_refresh_token
                )
                
                logger.info(f"✅ Token刷新成功: {account.email_address}")
                return True
                
            else:
                error_data = response.json() if response.content else {}
                error_code = error_data.get('error', 'unknown')
                logger.error(f"❌ Token刷新失败: {account.email_address} - {error_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Token刷新异常: {account.email_address} - {e}")
            return False
    
    @classmethod
    def batch_check_tokens(cls):
        """批量检查所有账户的token状态"""
        logger.info("开始批量检查token状态")
        
        accounts = get_all_accounts()
        results = {
            'healthy': [],
            'warning': [],
            'needs_refresh': [],
            'expired': [],
            'unknown': []
        }
        
        for account in accounts:
            health = cls.check_token_health(account)
            results[health].append(account.email_address)
        
        # 记录统计
        for status, emails in results.items():
            if emails:
                logger.info(f"{status.upper()}: {len(emails)} 个账户")
        
        return results
    
    @classmethod
    def auto_refresh_tokens(cls):
        """自动刷新需要刷新的token"""
        logger.info("开始自动刷新token")
        
        accounts = get_all_accounts()
        refresh_count = 0
        success_count = 0
        
        for account in accounts:
            health = cls.check_token_health(account)
            
            if health in ['needs_refresh', 'expired']:
                refresh_count += 1
                if cls.refresh_token(account):
                    success_count += 1
        
        logger.info(f"自动刷新完成: {success_count}/{refresh_count} 成功")
        return success_count, refresh_count
    
    @classmethod
    def get_token_report(cls):
        """生成token状态报告"""
        results = cls.batch_check_tokens()
        
        report = {
            'total_accounts': sum(len(emails) for emails in results.values()),
            'healthy_accounts': len(results['healthy']),
            'warning_accounts': len(results['warning']),
            'needs_refresh': len(results['needs_refresh']),
            'expired_accounts': len(results['expired']),
            'unknown_accounts': len(results['unknown']),
            'details': results
        }
        
        return report

def main():
    """主函数 - 用于命令行调用"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python token_manager.py check    # 检查token状态")
        print("  python token_manager.py refresh  # 自动刷新token")
        print("  python token_manager.py report   # 生成详细报告")
        return
    
    command = sys.argv[1]
    
    if command == "check":
        results = TokenManager.batch_check_tokens()
        print("\n📊 Token状态检查结果:")
        for status, emails in results.items():
            if emails:
                print(f"  {status.upper()}: {len(emails)} 个账户")
                for email in emails:
                    print(f"    - {email}")
    
    elif command == "refresh":
        success, total = TokenManager.auto_refresh_tokens()
        print(f"\n🔄 自动刷新结果: {success}/{total} 成功")
    
    elif command == "report":
        report = TokenManager.get_token_report()
        print("\n📋 Token状态详细报告:")
        print(f"  总账户数: {report['total_accounts']}")
        print(f"  健康账户: {report['healthy_accounts']}")
        print(f"  警告账户: {report['warning_accounts']}")
        print(f"  需要刷新: {report['needs_refresh']}")
        print(f"  已过期: {report['expired_accounts']}")
        print(f"  状态未知: {report['unknown_accounts']}")

if __name__ == "__main__":
    main()
