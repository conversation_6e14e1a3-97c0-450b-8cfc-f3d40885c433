<template>
  <div>
    <h1>Vue应用测试</h1>
    <p>如果你能看到这个页面，说明Vue应用基本正常。</p>
    <button @click="testApi">测试API连接</button>
    <p v-if="apiResult">API测试结果: {{ apiResult }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const apiResult = ref('')

const testApi = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/accounts')
    const data = await response.json()
    apiResult.value = `成功获取到 ${data.length} 个账户`
  } catch (error) {
    apiResult.value = `API错误: ${error.message}`
  }
}
</script>