# 前端部署到Netlify指南

## 🚀 部署步骤

### 1. 准备工作
- 确保后端服务器已部署并可访问
- 获取后端服务器的公网地址（如：https://your-server.com:8000）

### 2. 配置后端地址
编辑 `.env.production` 文件：
```
VITE_API_BASE_URL=https://your-backend-server.com:8000
```

### 3. Netlify部署方式

#### 方式1: Git自动部署（推荐）
1. 将代码推送到GitHub/GitLab
2. 在Netlify中连接仓库
3. 设置构建配置：
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Base directory: `frontend-vue`

#### 方式2: 手动部署
1. 本地构建：
   ```bash
   npm run build
   ```
2. 将 `dist` 文件夹拖拽到Netlify部署页面

### 4. 环境变量配置
在Netlify控制台中设置环境变量：
- Variable: `VITE_API_BASE_URL`
- Value: `https://your-backend-server.com:8000`

### 5. 域名配置
- Netlify会提供免费的 `.netlify.app` 域名
- 可以配置自定义域名
- 自动提供HTTPS证书

## 🔧 注意事项

### CORS配置
确保后端CORS配置包含Netlify域名：
```python
CORS(app, origins=[
    "http://localhost:5173",  # 开发环境
    "https://your-app.netlify.app",  # Netlify域名
    "https://your-custom-domain.com"  # 自定义域名
])
```

### 环境变量
- 开发环境使用 `.env.development`
- 生产环境使用 `.env.production`
- Netlify环境变量会覆盖文件配置

### 构建优化
- 自动代码分割
- 静态资源压缩
- CDN加速

## 📊 部署后验证

1. 访问Netlify提供的URL
2. 检查浏览器控制台API地址是否正确
3. 测试账户管理功能
4. 测试邮件获取功能
5. 检查网络请求是否正常

## 🚨 常见问题

### 1. CORS错误
- 检查后端CORS配置
- 确保包含Netlify域名

### 2. API请求失败
- 检查后端服务器是否可访问
- 确认API地址配置正确
- 检查防火墙设置

### 3. 路由404错误
- 确保 `netlify.toml` 配置正确
- 检查SPA重定向规则

### 4. 环境变量不生效
- 确保变量名以 `VITE_` 开头
- 重新构建和部署
