"""
诊断账户问题
"""

import requests
import json
from database import get_all_accounts

def diagnose_hotmail_accounts():
    """诊断hotmail账户问题"""
    print("🔍 诊断hotmail账户问题")
    print("=" * 60)
    
    # 获取所有账户
    accounts = get_all_accounts()
    hotmail_accounts = [acc for acc in accounts if acc.email_address.endswith('@hotmail.com')]
    
    if not hotmail_accounts:
        print("❌ 没有找到hotmail账户")
        return
    
    print(f"📧 找到 {len(hotmail_accounts)} 个hotmail账户")
    
    for i, account in enumerate(hotmail_accounts, 1):
        print(f"\n{i}. 诊断账户: {account.email_address}")
        print(f"   状态: {account.status}")
        print(f"   最后更新: {account.last_updated}")
        
        # 检查token格式
        if not account.refresh_token:
            print(f"   ❌ refresh_token为空")
            continue
            
        if len(account.refresh_token) < 50:
            print(f"   ⚠️ refresh_token长度异常: {len(account.refresh_token)}")
            
        if not account.client_id:
            print(f"   ❌ client_id为空")
            continue
            
        # 测试token请求
        test_token_request(account)

def test_token_request(account):
    """测试单个账户的token请求"""
    print(f"   🔄 测试token请求...")
    
    # 使用consumers端点（hotmail专用）
    token_url = "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
    
    data = {
        'client_id': account.client_id,
        'grant_type': 'refresh_token',
        'refresh_token': account.refresh_token,
    }
    
    try:
        response = requests.post(token_url, data=data, timeout=10)
        print(f"   📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Token请求成功")
            return True
        else:
            try:
                error_data = response.json()
                error_code = error_data.get('error', 'unknown')
                error_desc = error_data.get('error_description', '')
                
                print(f"   ❌ Token请求失败:")
                print(f"      错误代码: {error_code}")
                print(f"      错误描述: {error_desc[:100]}...")
                
                # 分析常见错误
                if error_code == 'invalid_grant':
                    print(f"   💡 建议: refresh_token已过期，需要重新获取")
                elif error_code == 'invalid_client':
                    print(f"   💡 建议: client_id无效或不匹配")
                elif error_code == 'invalid_request':
                    print(f"   💡 建议: 请求参数格式错误")
                    
            except json.JSONDecodeError:
                print(f"   ❌ 无法解析错误响应")
                
        return False
        
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def check_account_validity():
    """检查账户有效性统计"""
    print(f"\n📊 账户有效性统计")
    print("=" * 60)
    
    accounts = get_all_accounts()
    
    status_count = {}
    domain_count = {}
    
    for acc in accounts:
        # 统计状态
        status = acc.status
        status_count[status] = status_count.get(status, 0) + 1
        
        # 统计域名
        domain = acc.email_address.split('@')[1]
        domain_count[domain] = domain_count.get(domain, 0) + 1
    
    print("📈 按状态统计:")
    for status, count in status_count.items():
        print(f"   {status}: {count} 个账户")
    
    print("\n📈 按域名统计:")
    for domain, count in domain_count.items():
        print(f"   {domain}: {count} 个账户")
    
    # 找出问题账户
    error_accounts = [acc for acc in accounts if acc.status in ['Error', 'Token Invalid']]
    if error_accounts:
        print(f"\n❌ 有问题的账户 ({len(error_accounts)} 个):")
        for acc in error_accounts:
            print(f"   {acc.email_address} - {acc.status}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n💡 解决方案建议")
    print("=" * 60)
    
    print("1. 🔄 重新获取有效的refresh_token:")
    print("   - 删除失效的账户")
    print("   - 重新添加账户，获取新的token")
    
    print("\n2. 🧹 清理无效账户:")
    print("   - 在前端界面删除状态为Error的账户")
    print("   - 避免重复尝试失效的账户")
    
    print("\n3. 🔍 验证账户配置:")
    print("   - 确保client_id正确")
    print("   - 确保账户有邮件访问权限")
    
    print("\n4. 📧 使用有效的测试账户:")
    print("   - 建议使用您自己的hotmail账户进行测试")
    print("   - 确保账户可以正常登录和访问邮件")

if __name__ == "__main__":
    print("🚀 开始诊断账户问题")
    
    diagnose_hotmail_accounts()
    check_account_validity()
    suggest_solutions()
    
    print("\n" + "=" * 60)
    print("✅ 诊断完成")
    print("\n📋 总结:")
    print("   1. 所有hotmail账户token都已失效")
    print("   2. 需要删除这些账户并重新添加")
    print("   3. 建议使用您自己的有效账户进行测试")
