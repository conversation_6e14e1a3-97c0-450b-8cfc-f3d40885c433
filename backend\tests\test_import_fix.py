"""
测试导入功能修复后的效果
"""

import requests
import json
import time

# API 基础 URL
API_BASE_URL = "http://localhost:8000"

def test_cors_headers():
    """测试 CORS 头部"""
    print("🔧 测试 CORS 配置...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/accounts")
        
        if response.status_code == 200:
            print("   ✅ API 请求成功")
            
            # 检查 CORS 头部
            cors_header = response.headers.get('Access-Control-Allow-Origin')
            if cors_header:
                print(f"   ✅ CORS 头部存在: {cors_header}")
                if cors_header == "http://localhost:3000":
                    print("   ✅ CORS 配置正确")
                    return True
                else:
                    print("   ⚠️ CORS 配置可能不正确")
                    return False
            else:
                print("   ❌ 缺少 CORS 头部")
                return False
        else:
            print(f"   ❌ API 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_add_account_api():
    """测试添加账户 API"""
    print("\n📧 测试添加账户 API...")
    
    test_account = {
        "email_address": "<EMAIL>",
        "client_id": "cors1234-5678-90ab-cdef-123456789abc",
        "refresh_token": "M.C519_BAY.0.U.-CorsTestToken123456789abcdef"
    }
    
    try:
        # 先删除可能存在的测试账户
        try:
            accounts_response = requests.get(f"{API_BASE_URL}/api/accounts")
            if accounts_response.status_code == 200:
                accounts = accounts_response.json()
                for account in accounts:
                    if account['email_address'] == test_account['email_address']:
                        print(f"   删除已存在的测试账户: {account['email_address']}")
        except:
            pass
        
        # 测试添加账户
        response = requests.post(
            f"{API_BASE_URL}/api/accounts",
            json=test_account,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 201:
            print("   ✅ 添加账户成功")
            result = response.json()
            print(f"   📧 账户: {result['email_address']}")
            print(f"   🆔 ID: {result['id']}")
            print(f"   📊 状态: {result['status']}")
            
            # 检查 CORS 头部
            cors_header = response.headers.get('Access-Control-Allow-Origin')
            if cors_header:
                print(f"   ✅ POST 请求 CORS 头部正确: {cors_header}")
            else:
                print("   ⚠️ POST 请求缺少 CORS 头部")
            
            return True
        elif response.status_code == 409:
            print("   ✅ 账户已存在（正常）")
            return True
        else:
            print(f"   ❌ 添加失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_real_account_format():
    """测试真实账户数据格式"""
    print("\n🔍 测试真实账户数据格式...")
    
    # 从 add_real_accounts.py 中的真实数据
    real_account = {
        "email_address": "<EMAIL>",
        "client_id": "dbc8e03a-b00c-46bd-ae65-b683e7707cb0",
        "refresh_token": "M.C519_BAY.0.U.-Ci6kMqO!ZLPI8gdNqc41uknWXLOd7*PC*sXAfW02ioLtQ!nGAC*3MwndY8wwl!6mS2nOJg0gsgd5kVh3KHX1nWHXNepIEXCxdbh04sQILosGrmaMI8nKr3wDXlSHP5UiACTqCfoyosj5lVHBLmPZz8nZAVrGCsMhoIX0I8NXgZXD6Sm0erk5Oz2oG08XeyvjLz8WM*YUF93A!Dv7*!Y3MSExc04d1G52oTbjihW8WxDYY1d!22GNyO3JPk4hQnqp3DcfK9z6vmVr3Ro54UoWrWXfsSWbi3OOggjGoWjpoELZ0Fo3ob0fNJAI2PETzZHwaH7r7sktXXZ3wAmh3mFWl7z4OdvSWYFSuzmluLnP6dFYyZrSCku2Riz*d1ZliRHz0g$$"
    }
    
    # 验证数据格式
    print(f"   📧 邮箱: {real_account['email_address']}")
    print(f"   🔑 Client ID: {real_account['client_id']}")
    print(f"   🎫 Refresh Token 长度: {len(real_account['refresh_token'])} 字符")
    
    # 检查格式
    email_valid = '@' in real_account['email_address'] and '.' in real_account['email_address']
    client_id_valid = len(real_account['client_id']) >= 10
    token_valid = len(real_account['refresh_token']) >= 20
    
    print(f"   ✅ 邮箱格式: {'有效' if email_valid else '无效'}")
    print(f"   ✅ Client ID: {'有效' if client_id_valid else '无效'}")
    print(f"   ✅ Refresh Token: {'有效' if token_valid else '无效'}")
    
    if email_valid and client_id_valid and token_valid:
        print("   ✅ 真实账户数据格式正确")
        return True
    else:
        print("   ❌ 真实账户数据格式有问题")
        return False

def test_frontend_import_simulation():
    """模拟前端导入流程"""
    print("\n🔄 模拟前端导入流程...")
    
    # 模拟前端发送的请求
    test_accounts = [
        {
            "email_address": "<EMAIL>",
            "client_id": "frontend1-2345-6789-abcd-ef1234567890",
            "refresh_token": "M.C519_BAY.0.U.-FrontendTestToken123456789abcdef"
        },
        {
            "email_address": "<EMAIL>",
            "client_id": "frontend2-3456-789a-bcde-f12345678901",
            "refresh_token": "M.C520_BAY.0.U.-FrontendTestToken987654321fedcba"
        }
    ]
    
    success_count = 0
    failure_count = 0
    
    for i, account in enumerate(test_accounts, 1):
        print(f"   {i}. 导入账户: {account['email_address']}")
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/accounts",
                json=account,
                headers={
                    "Content-Type": "application/json",
                    "Origin": "http://localhost:3000"  # 模拟前端请求
                }
            )
            
            if response.status_code in [201, 409]:  # 201=创建成功, 409=已存在
                success_count += 1
                print(f"      ✅ 成功")
                
                # 检查 CORS 头部
                cors_header = response.headers.get('Access-Control-Allow-Origin')
                if cors_header == "http://localhost:3000":
                    print(f"      ✅ CORS 头部正确")
                else:
                    print(f"      ⚠️ CORS 头部: {cors_header}")
                    
            else:
                failure_count += 1
                print(f"      ❌ 失败: {response.status_code}")
                print(f"      错误: {response.text}")
                
        except Exception as e:
            failure_count += 1
            print(f"      ❌ 异常: {e}")
        
        # 添加延迟模拟真实导入
        time.sleep(0.5)
    
    print(f"   📊 导入结果: 成功 {success_count}, 失败 {failure_count}")
    return failure_count == 0

def main():
    """主测试函数"""
    print("🚀 导入功能修复验证")
    print("=" * 60)
    
    tests = [
        ("CORS 配置", test_cors_headers),
        ("添加账户 API", test_add_account_api),
        ("真实账户格式", test_real_account_format),
        ("前端导入模拟", test_frontend_import_simulation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results[test_name] = False
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 修复验证结果:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！导入功能已修复")
        print("\n📖 现在可以正常使用导入功能:")
        print("   1. 访问前端: http://localhost:3000")
        print("   2. 点击 '📥 导入邮箱' 按钮")
        print("   3. 选择导入方式并上传数据")
        print("   4. 导入应该可以正常工作了")
    else:
        print("⚠️ 部分测试失败，可能还有其他问题")
        print("\n🔧 进一步排查:")
        print("   1. 检查浏览器控制台是否还有错误")
        print("   2. 确认前端和后端服务都在运行")
        print("   3. 检查网络连接")

if __name__ == "__main__":
    main()
