<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <header class="app-header">
      <h1 class="app-title">📧 多账户邮件聚合管理系统</h1>
      <div class="app-status">
        <span class="status-indicator">{{ connectionStatus ? '🟢' : '🔴' }}</span>
        <span class="status-text">{{ connectionStatus ? '服务正常' : '服务异常' }}</span>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main">
      <!-- 左侧控制面板 -->
      <aside class="control-panel">
        <!-- 测试AccountList组件 -->
        <AccountList
          :accounts="accounts"
          :loading="isAccountsLoading"
          v-model="selectedAccounts"
          @delete-account="handleDeleteAccount"
        />
      </aside>

      <!-- 中间邮件列表区域 -->
      <div class="email-list-panel">
        <h2>邮件列表</h2>
        <p>{{ statusMessage }}</p>
      </div>

      <!-- 右侧邮件详情区域 -->
      <div class="email-detail-panel">
        <h2>邮件详情</h2>
        <p>请选择邮件</p>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as api from './services/api'
import AccountList from './components/AccountList.vue'

// 响应式数据
const accounts = ref([])
const selectedAccounts = ref([])
const statusMessage = ref('📭 请选择账户和文件夹，然后点击"获取邮件"')
const isAccountsLoading = ref(false)
const connectionStatus = ref(true)

// 初始化应用
onMounted(async () => {
  await loadAccounts()
})

// 加载账户列表
const loadAccounts = async () => {
  try {
    isAccountsLoading.value = true
    accounts.value = await api.fetchAccounts()
    connectionStatus.value = true
  } catch (error) {
    console.error('加载账户失败:', error)
    connectionStatus.value = false
  } finally {
    isAccountsLoading.value = false
  }
}

// 处理账户删除
const handleDeleteAccount = async (account) => {
  try {
    await api.deleteAccount(account.email_address)
    accounts.value = accounts.value.filter(acc => acc.email_address !== account.email_address)
    selectedAccounts.value = selectedAccounts.value.filter(email => email !== account.email_address)
  } catch (error) {
    console.error('删除账户失败:', error)
    alert(`删除账户失败：${error.message}`)
  }
}
</script>

<style>
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2563eb;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --surface-color: #ffffff;
  --border-color: #e2e8f0;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: #f8fafc;
  font-size: 14px;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.app-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
}

.app-main {
  flex: 1;
  display: grid;
  grid-template-columns: 300px 1fr 400px;
  gap: 0;
  min-height: 0;
}

.control-panel, .email-list-panel, .email-detail-panel {
  background: var(--surface-color);
  border-right: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.email-detail-panel {
  border-right: none;
}
</style>