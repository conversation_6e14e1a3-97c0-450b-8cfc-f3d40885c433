<template>
  <div v-if="isOpen" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">📧 获取邮件设置</h2>
        <button class="modal-close" @click="close">✕</button>
      </div>

      <div class="modal-body">
        <!-- 选中的账户统计 -->
        <div class="section">
          <h3 class="section-title">📊 获取邮件统计</h3>
          <div class="account-summary">
            <div v-if="selectedAccounts.length === 0" class="no-accounts">
              ⚠️ 请先选择要获取邮件的账户
            </div>
            <div v-else class="account-stats">
              <div class="stat-item">
                <span class="stat-label">选中账户：</span>
                <span class="stat-value">{{ selectedAccounts.length }} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">获取范围：</span>
                <span class="stat-value">
                  {{ selectedAccounts.length === 1 
                    ? '单个账户的所有邮件' 
                    : `${selectedAccounts.length} 个账户的所有邮件` 
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 文件夹选择 -->
        <div class="section">
          <h3 class="section-title">📁 文件夹选择</h3>
          <div class="folder-options">
            <label class="checkbox-item">
              <input type="checkbox" v-model="folders" value="inbox">
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">📥 收件箱</span>
            </label>
            
            <label class="checkbox-item">
              <input type="checkbox" v-model="folders" value="junk">
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">🗑️ 垃圾箱</span>
            </label>
            
            <label class="checkbox-item">
              <input type="checkbox" v-model="folders" value="sent">
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">📤 已发送</span>
            </label>
            
            <label class="checkbox-item">
              <input type="checkbox" v-model="folders" value="drafts">
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">📝 草稿箱</span>
            </label>
          </div>
        </div>

        <!-- 日期范围选择 -->
        <div class="section">
          <h3 class="section-title">📅 日期范围</h3>
          <div class="date-options">
            <label class="radio-item">
              <input type="radio" v-model="dateRange" value="all" name="dateRange">
              <span class="radio-custom"></span>
              <span class="radio-text">所有邮件</span>
            </label>
            
            <label class="radio-item">
              <input type="radio" v-model="dateRange" value="today" name="dateRange">
              <span class="radio-custom"></span>
              <span class="radio-text">今天</span>
            </label>
            
            <label class="radio-item">
              <input type="radio" v-model="dateRange" value="week" name="dateRange">
              <span class="radio-custom"></span>
              <span class="radio-text">最近一周</span>
            </label>
            
            <label class="radio-item">
              <input type="radio" v-model="dateRange" value="month" name="dateRange">
              <span class="radio-custom"></span>
              <span class="radio-text">最近一个月</span>
            </label>
            
            <label class="radio-item">
              <input type="radio" v-model="dateRange" value="custom" name="dateRange">
              <span class="radio-custom"></span>
              <span class="radio-text">自定义日期</span>
            </label>
          </div>

          <!-- 自定义日期范围 -->
          <div v-if="dateRange === 'custom'" class="custom-date-range">
            <div class="date-input-group">
              <label>开始日期：</label>
              <input type="date" v-model="startDate" class="date-input">
            </div>
            <div class="date-input-group">
              <label>结束日期：</label>
              <input type="date" v-model="endDate" class="date-input">
            </div>
          </div>
        </div>


      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="close">取消</button>
        <button 
          class="btn btn-primary" 
          @click="handleFetch"
          :disabled="selectedAccounts.length === 0 || folders.length === 0"
        >
          <span class="btn-icon">📧</span>
          <span class="btn-text">开始获取</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  selectedAccounts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'fetch-emails'])

// 获取邮件的配置
const folders = ref(['inbox']) // 默认选择收件箱
const dateRange = ref('week') // 默认最近一周
const startDate = ref('')
const endDate = ref('')


// 设置默认的自定义日期范围
const setDefaultDateRange = () => {
  const today = new Date()
  const weekAgo = new Date(today)
  weekAgo.setDate(weekAgo.getDate() - 7)
  
  endDate.value = today.toISOString().split('T')[0]
  startDate.value = weekAgo.toISOString().split('T')[0]
}

// 监听弹窗打开，设置默认值
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    setDefaultDateRange()
  }
})

// 关闭弹窗
const close = () => {
  emit('close')
}

// 处理点击遮罩层
const handleOverlayClick = () => {
  close()
}

// 处理获取邮件
const handleFetch = () => {
  const config = {
    folders: folders.value,
    dateRange: dateRange.value,
    startDate: startDate.value,
    endDate: endDate.value
  }
  
  emit('fetch-emails', config)
  close()
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.section {
  margin-bottom: 24px;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

/* 账户统计显示 */
.account-summary {
  padding: 16px;
  background: #f1f5f9;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
}

.no-accounts {
  color: #dc2626;
  font-size: 0.9rem;
  text-align: center;
  padding: 8px;
  font-weight: 500;
}

.account-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 0.9rem;
  color: #1e293b;
  font-weight: 600;
}



/* 文件夹和选项样式 */
.folder-options,
.date-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}



.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.checkbox-item:hover,
.radio-item:hover {
  background: #f1f5f9;
}

.checkbox-custom,
.radio-custom {
  width: 16px;
  height: 16px;
  border: 2px solid #cbd5e1;
  border-radius: 3px;
  position: relative;
  flex-shrink: 0;
  background: #ffffff;
}

.radio-custom {
  border-radius: 50%;
}

input[type="checkbox"],
input[type="radio"] {
  display: none;
}

input[type="checkbox"]:checked + .checkbox-custom {
  background: #3b82f6;
  border-color: #3b82f6;
}

input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

input[type="radio"]:checked + .radio-custom {
  border-color: #3b82f6;
}

input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
}

.checkbox-text,
.radio-text {
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

/* 自定义日期范围 */
.custom-date-range {
  margin-top: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-input-group label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.date-input {
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.9rem;
  background: #ffffff;
  color: #374151;
}

.date-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 弹窗底部 */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #ffffff;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
  color: #374151;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 响应式 */
@media (max-width: 768px) {
  .modal-content {
    margin: 10px;
    max-height: 95vh;
  }
  
  .folder-options,
  .date-options {
    grid-template-columns: 1fr;
  }
  
  .custom-date-range {
    grid-template-columns: 1fr;
  }
}
</style>