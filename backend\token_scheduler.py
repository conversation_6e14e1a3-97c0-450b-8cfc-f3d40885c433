"""
Token定期刷新调度器
"""

import schedule
import time
import logging
from token_manager import TokenManager

logger = logging.getLogger(__name__)

class TokenScheduler:
    """Token定期刷新调度器"""
    
    def __init__(self):
        self.setup_schedule()
    
    def setup_schedule(self):
        """设置定期任务"""
        
        # 每天检查一次token状态
        schedule.every().day.at("02:00").do(self.daily_token_check)
        
        # 每周自动刷新需要刷新的token
        schedule.every().sunday.at("03:00").do(self.weekly_token_refresh)
        
        # 每小时检查一次即将过期的token
        schedule.every().hour.do(self.hourly_urgent_check)
        
        logger.info("Token调度器已设置")
    
    def daily_token_check(self):
        """每日token状态检查"""
        logger.info("开始每日token状态检查")
        
        try:
            report = TokenManager.get_token_report()
            
            # 记录统计信息
            logger.info(f"Token状态统计:")
            logger.info(f"  总账户: {report['total_accounts']}")
            logger.info(f"  健康: {report['healthy_accounts']}")
            logger.info(f"  警告: {report['warning_accounts']}")
            logger.info(f"  需刷新: {report['needs_refresh']}")
            logger.info(f"  已过期: {report['expired_accounts']}")
            
            # 如果有需要关注的账户，发出警告
            if report['needs_refresh'] > 0:
                logger.warning(f"有 {report['needs_refresh']} 个账户需要刷新token")
            
            if report['expired_accounts'] > 0:
                logger.error(f"有 {report['expired_accounts']} 个账户token已过期")
                
        except Exception as e:
            logger.error(f"每日token检查失败: {e}")
    
    def weekly_token_refresh(self):
        """每周自动刷新token"""
        logger.info("开始每周token自动刷新")
        
        try:
            success, total = TokenManager.auto_refresh_tokens()
            logger.info(f"每周token刷新完成: {success}/{total} 成功")
            
            if total > 0 and success < total:
                logger.warning(f"有 {total - success} 个账户token刷新失败")
                
        except Exception as e:
            logger.error(f"每周token刷新失败: {e}")
    
    def hourly_urgent_check(self):
        """每小时检查紧急情况"""
        try:
            results = TokenManager.batch_check_tokens()
            
            # 只处理紧急情况
            urgent_accounts = results.get('expired', [])
            
            if urgent_accounts:
                logger.warning(f"发现 {len(urgent_accounts)} 个过期账户，尝试立即刷新")
                
                # 尝试刷新过期账户
                for email in urgent_accounts:
                    # 这里可以添加紧急刷新逻辑
                    pass
                    
        except Exception as e:
            logger.error(f"紧急检查失败: {e}")
    
    def run(self):
        """运行调度器"""
        logger.info("Token调度器开始运行")
        
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                logger.info("Token调度器停止")
                break
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(60)

def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    scheduler = TokenScheduler()
    scheduler.run()

if __name__ == "__main__":
    main()
