"""
生产环境配置
"""

import os

class ProductionConfig:
    """生产环境配置类"""
    
    # 服务器配置
    HOST = '0.0.0.0'  # 监听所有接口
    PORT = int(os.environ.get('PORT', 8000))
    DEBUG = False
    
    # 数据库配置
    DATABASE_PATH = os.environ.get('DATABASE_PATH', '/app/data/accounts.db')
    
    # CORS配置
    ALLOWED_ORIGINS = [
        # 从环境变量获取允许的域名
        os.environ.get('FRONTEND_URL', 'https://your-app.netlify.app'),
        # 可以添加多个域名
        'https://*.netlify.app',
    ]
    
    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', '/app/logs/app.log')
    
    # 邮件配置
    EMAIL_LIMIT = int(os.environ.get('EMAIL_LIMIT', 500))
    
    @classmethod
    def get_cors_origins(cls):
        """获取CORS允许的源"""
        origins = []
        
        # 从环境变量获取
        frontend_url = os.environ.get('FRONTEND_URL')
        if frontend_url:
            origins.append(frontend_url)
            
        # 添加默认的开发环境地址（如果需要）
        if cls.DEBUG:
            origins.extend([
                'http://localhost:3000',
                'http://localhost:5173',
                'http://localhost:5174'
            ])
            
        return origins
