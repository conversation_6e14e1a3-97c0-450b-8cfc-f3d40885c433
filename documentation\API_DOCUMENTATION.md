npm# 多账户邮件聚合 API 服务文档

## 概述

这是一个基于 Flask 的 RESTful API 服务，用于管理多个微软邮箱账户并获取邮件。该服务提供了完整的账户管理和邮件获取功能。

## 服务器信息

- **框架**: Flask
- **端口**: 8000
- **基础URL**: `http://localhost:8000`
- **数据库**: SQLite (accounts.db)

## API 接口

### 1. 服务状态

#### GET /
获取 API 服务状态信息

**响应示例**:
```json
{
    "message": "多账户邮件聚合 API 服务",
    "status": "运行中",
    "version": "1.0.0",
    "endpoints": {
        "accounts": "/api/accounts",
        "docs": "请查看源代码了解 API 接口"
    }
}
```

### 2. 账户管理

#### POST /api/accounts
添加新的邮箱账户

**请求体**:
```json
{
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "refresh_token": "your-refresh-token"
}
```

**响应示例** (201 Created):
```json
{
    "id": 1,
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "status": "OK",
    "last_updated": "2025-08-01T12:00:00.000000"
}
```

**错误响应** (409 Conflict):
```json
{
    "error": "ACCOUNT_EXISTS",
    "message": "邮箱地址 <EMAIL> 已存在",
    "status_code": 409
}
```

#### GET /api/accounts
获取所有账户信息

**响应示例**:
```json
[
    {
        "id": 1,
        "email_address": "<EMAIL>",
        "client_id": "your-client-id",
        "status": "OK",
        "last_updated": "2025-08-01T12:00:00.000000"
    }
]
```

#### GET /api/accounts/{email_address}
获取指定账户信息

**响应示例** (200 OK):
```json
{
    "id": 1,
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "status": "OK",
    "last_updated": "2025-08-01T12:00:00.000000"
}
```

**错误响应** (404 Not Found):
```json
{
    "error": "ACCOUNT_NOT_FOUND",
    "message": "邮箱地址 <EMAIL> 不存在",
    "status_code": 404
}
```

#### DELETE /api/accounts/{email_address}
删除指定账户

**响应示例** (200 OK):
```json
{
    "message": "成功删除账户: <EMAIL>",
    "email_address": "<EMAIL>"
}
```

### 3. 邮件获取 (核心功能)

#### GET /api/mail/{email_address}
获取指定账户的邮件

**查询参数**:
- `folder` (可选): 邮箱文件夹，默认 'inbox'
  - 支持: inbox, junk, sent, drafts, deleted
- `limit` (可选): 获取邮件数量，默认 5，范围 1-50

**请求示例**:
```
GET /api/mail/<EMAIL>?folder=inbox&limit=3
```

**成功响应** (200 OK):
```json
{
    "email_address": "<EMAIL>",
    "folder": "inbox",
    "total_count": 2,
    "messages": [
        {
            "subject": "连接到 Microsoft 帐户的新应用",
            "sender": "Microsoft 帐户团队 (<EMAIL>)",
            "recipient": "<EMAIL>",
            "date": "2025-07-01 19:04:01",
            "body": "邮件正文内容..."
        },
        {
            "subject": "欢迎使用你的新 Outlook.com 帐户",
            "sender": "Outlook 团队 (<EMAIL>)",
            "recipient": "<EMAIL>",
            "date": "2025-07-02 10:03:48",
            "body": "邮件正文内容..."
        }
    ]
}
```

**错误响应**:

**Token 失效** (401 Unauthorized):
```json
{
    "error": "TOKEN失效",
    "message": "Token 获取失败: invalid_grant",
    "email_address": "<EMAIL>",
    "status_code": 401
}
```

**登录失败** (403 Forbidden):
```json
{
    "error": "IMAP错误",
    "message": "IMAP 连接或操作失败: authentication failed",
    "email_address": "<EMAIL>",
    "status_code": 403
}
```

**参数错误** (400 Bad Request):
```json
{
    "error": "INVALID_LIMIT",
    "message": "limit 参数必须在 1-50 之间",
    "status_code": 400
}
```

## 账户状态说明

- **OK**: 账户正常，可以获取邮件
- **Token Invalid**: Refresh Token 失效，需要重新授权
- **Login Failed**: IMAP 登录失败
- **Error**: 其他错误

## 使用示例

### Python 示例

```python
import requests

BASE_URL = "http://localhost:8000"

# 1. 添加账户
account_data = {
    "email_address": "<EMAIL>",
    "client_id": "your-client-id",
    "refresh_token": "your-refresh-token"
}

response = requests.post(f"{BASE_URL}/api/accounts", json=account_data)
print(f"添加账户: {response.status_code}")

# 2. 获取邮件
response = requests.get(
    f"{BASE_URL}/api/mail/<EMAIL>",
    params={"folder": "inbox", "limit": 5}
)

if response.status_code == 200:
    result = response.json()
    print(f"获取到 {result['total_count']} 封邮件")
    for msg in result['messages']:
        print(f"- {msg['subject']} (来自: {msg['sender']})")
else:
    print(f"获取邮件失败: {response.json()}")
```

### cURL 示例

```bash
# 添加账户
curl -X POST http://localhost:8000/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "email_address": "<EMAIL>",
    "client_id": "your-client-id", 
    "refresh_token": "your-refresh-token"
  }'

# 获取邮件
curl "http://localhost:8000/api/mail/<EMAIL>?folder=inbox&limit=3"

# 获取所有账户
curl http://localhost:8000/api/accounts

# 删除账户
curl -X DELETE http://localhost:8000/api/accounts/<EMAIL>
```

## 错误处理

所有错误响应都遵循统一格式:
```json
{
    "error": "ERROR_TYPE",
    "message": "详细错误信息",
    "status_code": 400
}
```

常见 HTTP 状态码:
- 200: 成功
- 201: 创建成功
- 400: 请求参数错误
- 401: Token 失效
- 403: 认证失败
- 404: 资源不存在
- 409: 资源冲突
- 500: 服务器内部错误

## 注意事项

1. **安全性**: refresh_token 是敏感信息，API 响应中不会返回
2. **频率限制**: 建议控制邮件获取频率，避免过于频繁的请求
3. **超时设置**: 邮件获取可能需要较长时间，建议设置适当的超时时间
4. **错误重试**: 对于临时性错误，可以实现重试机制
5. **状态监控**: 定期检查账户状态，及时处理 Token 失效等问题
