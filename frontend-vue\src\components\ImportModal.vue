<template>
  <!-- 导入邮箱模态框 -->
  <div 
    v-if="isOpen" 
    class="modal-overlay" 
    @click="handleOverlayClick"
  >
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">📥 导入邮箱账户</h2>
        <button type="button" class="modal-close" @click="closeModal">
          <span>✕</span>
        </button>
      </div>

      <div class="modal-body">
        <!-- 导入方式选择 -->
        <div class="import-tabs">
          <button 
            type="button" 
            :class="['tab-button', { active: currentTab === 'file' }]" 
            @click="switchTab('file')"
          >
            <span class="tab-icon">📁</span>
            文件导入
          </button>
          <button 
            type="button" 
            :class="['tab-button', { active: currentTab === 'manual' }]" 
            @click="switchTab('manual')"
          >
            <span class="tab-icon">✏️</span>
            手动输入
          </button>
          <button 
            type="button" 
            :class="['tab-button', { active: currentTab === 'clipboard' }]" 
            @click="switchTab('clipboard')"
          >
            <span class="tab-icon">📋</span>
            剪贴板
          </button>
        </div>

        <!-- 文件导入面板 -->
        <div v-show="currentTab === 'file'" class="tab-content active">
          <div class="import-section">
            <h3 class="section-subtitle">选择导入文件</h3>
            <div 
              class="file-upload-area" 
              :class="{ dragover: isDragOver }"
              @click="triggerFileInput"
              @dragover.prevent="handleDragOver"
              @dragleave.prevent="handleDragLeave"
              @drop.prevent="handleFileDrop"
            >
              <input 
                ref="fileInput" 
                type="file" 
                accept=".csv,.json,.txt" 
                style="display: none;"
                @change="handleFileSelect"
              >
              <div class="upload-placeholder">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                  <strong>点击选择文件</strong> 或拖拽文件到此处
                </div>
                <div class="upload-hint">
                  支持 CSV、JSON、TXT 格式
                </div>
              </div>
            </div>

            <div class="format-info">
              <h4>支持的文件格式：</h4>
              <div class="format-examples">
                <div class="format-item">
                  <strong>CSV 格式：</strong>
                  <code>email,client_id,refresh_token</code>
                </div>
                <div class="format-item">
                  <strong>JSON 格式：</strong>
                  <code>[{"email_address":"...","client_id":"...","refresh_token":"..."}]</code>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 手动输入面板 -->
        <div v-show="currentTab === 'manual'" class="tab-content">
          <div class="import-section">
            <h3 class="section-subtitle">手动添加账户</h3>
            <form class="manual-form" @submit.prevent="addManualAccount">
              <div class="form-group">
                <label for="manualEmail" class="form-label">邮箱地址</label>
                <input 
                  id="manualEmail"
                  v-model="manualForm.email" 
                  type="email" 
                  class="form-input" 
                  placeholder="<EMAIL>" 
                  required
                >
              </div>
              <div class="form-group">
                <label for="manualClientId" class="form-label">Client ID</label>
                <input 
                  id="manualClientId"
                  v-model="manualForm.clientId" 
                  type="text" 
                  class="form-input" 
                  placeholder="应用程序客户端 ID" 
                  required
                >
              </div>
              <div class="form-group">
                <label for="manualRefreshToken" class="form-label">Refresh Token</label>
                <textarea 
                  id="manualRefreshToken"
                  v-model="manualForm.refreshToken" 
                  class="form-textarea" 
                  placeholder="OAuth2 刷新令牌" 
                  required
                ></textarea>
              </div>
              <button type="submit" class="btn btn-secondary btn-small">
                <span class="btn-icon">➕</span>
                添加到列表
              </button>
            </form>

            <div class="manual-list">
              <h4>待导入账户列表：</h4>
              <div class="manual-accounts">
                <div v-if="pendingAccounts.length === 0" class="empty-list">暂无账户</div>
                <div 
                  v-else
                  v-for="(account, index) in pendingAccounts" 
                  :key="index"
                  class="manual-account-item"
                >
                  <div class="account-info">
                    <div class="account-email">{{ account.email_address }}</div>
                    <div class="account-client">{{ account.client_id.substring(0, 20) }}...</div>
                  </div>
                  <button 
                    type="button" 
                    class="remove-account" 
                    @click="removeManualAccount(index)"
                  >
                    ✕
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 剪贴板导入面板 -->
        <div v-show="currentTab === 'clipboard'" class="tab-content">
          <div class="import-section">
            <h3 class="section-subtitle">从剪贴板导入</h3>
            <div class="clipboard-area">
              <textarea
                v-model="clipboardText"
                class="clipboard-textarea"
                placeholder="粘贴账户数据到此处...&#10;&#10;支持格式：&#10;1. 每行一个账户：email,client_id,refresh_token&#10;2. JSON 数组格式&#10;3. 制表符分隔的数据"
              ></textarea>
              <button type="button" class="btn btn-secondary btn-small" @click="parseClipboard">
                <span class="btn-icon">🔍</span>
                解析数据
              </button>
            </div>
          </div>
        </div>

        <!-- 预览和验证区域 -->
        <div v-if="showPreview" class="preview-section">
          <h3 class="section-subtitle">导入预览</h3>
          <div class="preview-stats">
            <div class="stat-item stat-valid">
              <span>✓</span>
              <span>有效: {{ validAccounts.length }}</span>
            </div>
            <div class="stat-item stat-invalid">
              <span>✗</span>
              <span>无效: {{ invalidAccounts.length }}</span>
            </div>
            <div class="stat-item stat-duplicate">
              <span>⚠</span>
              <span>重复: {{ duplicateAccounts.length }}</span>
            </div>
          </div>
          <div class="preview-list">
            <div 
              v-for="(account, index) in allPreviewAccounts" 
              :key="index"
              :class="['preview-item', account.status]"
            >
              <div class="preview-info">
                <div class="preview-email">{{ account.email_address }}</div>
                <div v-if="account.reason" class="preview-reason">{{ account.reason }}</div>
              </div>
              <div :class="['preview-status', `status-${account.status}`]">
                {{ account.status === 'valid' ? '有效' : account.status === 'invalid' ? '无效' : '重复' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 进度区域 -->
        <div v-if="showProgress" class="progress-section">
          <h3 class="section-subtitle">导入进度</h3>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
          </div>
          <div class="progress-text">{{ progressText }}</div>
          <div class="progress-details">{{ progressDetails }}</div>
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @click="closeModal" :disabled="isImporting">
          取消
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          :disabled="!canStartImport || isImporting"
          @click="startImport"
        >
          <span class="btn-icon">📥</span>
          {{ isImporting ? '导入中...' : '开始导入' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  existingAccounts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'import-accounts'])

// 响应式数据
const currentTab = ref('file')
const isDragOver = ref(false)
const fileInput = ref(null)
const manualForm = ref({
  email: '',
  clientId: '',
  refreshToken: ''
})
const clipboardText = ref('')
const pendingAccounts = ref([])
const showPreview = ref(false)
const showProgress = ref(false)
const progress = ref(0)
const progressText = ref('')
const progressDetails = ref('')
const isImporting = ref(false)

// 计算属性
const validAccounts = computed(() => {
  return pendingAccounts.value.filter(account => {
    const validation = validateAccountData(account)
    const isDuplicate = props.existingAccounts.some(existing => 
      existing.email_address === account.email_address
    )
    return validation.isValid && !isDuplicate
  })
})

const invalidAccounts = computed(() => {
  return pendingAccounts.value.filter(account => {
    const validation = validateAccountData(account)
    return !validation.isValid
  })
})

const duplicateAccounts = computed(() => {
  return pendingAccounts.value.filter(account => {
    const validation = validateAccountData(account)
    const isDuplicate = props.existingAccounts.some(existing => 
      existing.email_address === account.email_address
    )
    return validation.isValid && isDuplicate
  })
})

const allPreviewAccounts = computed(() => {
  return [
    ...validAccounts.value.map(acc => ({ ...acc, status: 'valid' })),
    ...invalidAccounts.value.map(acc => ({ ...acc, status: 'invalid', reason: '数据不完整' })),
    ...duplicateAccounts.value.map(acc => ({ ...acc, status: 'duplicate', reason: '邮箱已存在' }))
  ]
})

const canStartImport = computed(() => {
  return validAccounts.value.length > 0 && !isImporting.value
})

// 监听模态框打开状态
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    resetModal()
    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown)
  } else {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown)
  }
})

// 方法
const closeModal = () => {
  emit('close')
}

const handleOverlayClick = (event) => {
  if (event.target === event.currentTarget) {
    closeModal()
  }
}

// 处理键盘事件
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    closeModal()
  }
}

const resetModal = () => {
  currentTab.value = 'file'
  isDragOver.value = false
  manualForm.value = { email: '', clientId: '', refreshToken: '' }
  clipboardText.value = ''
  pendingAccounts.value = []
  showPreview.value = false
  showProgress.value = false
  progress.value = 0
  progressText.value = ''
  progressDetails.value = ''
  isImporting.value = false
}

const switchTab = (tabName) => {
  currentTab.value = tabName
  showPreview.value = false
}

// 文件处理
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleFileDrop = (event) => {
  isDragOver.value = false
  const files = event.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

const processFile = async (file) => {
  try {
    const text = await readFileAsText(file)
    const accounts = parseFileContent(text, file.name)
    if (accounts.length > 0) {
      pendingAccounts.value = accounts
      showPreview.value = true
    } else {
      alert('文件中没有找到有效的账户数据')
    }
  } catch (error) {
    console.error('文件处理失败:', error)
    alert(`文件处理失败: ${error.message}`)
  }
}

const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file, 'UTF-8')
  })
}

const parseFileContent = (content, filename) => {
  const extension = filename.split('.').pop().toLowerCase()
  
  try {
    if (extension === 'json') {
      return parseJSONContent(content)
    } else if (extension === 'csv') {
      return parseCSVContent(content)
    } else {
      return parseAutoDetect(content)
    }
  } catch (error) {
    throw new Error(`文件格式解析失败: ${error.message}`)
  }
}

const parseJSONContent = (content) => {
  const data = JSON.parse(content)
  if (!Array.isArray(data)) {
    throw new Error('JSON 文件必须包含账户数组')
  }
  return data.map((item, index) => {
    if (!item.email_address || !item.client_id || !item.refresh_token) {
      throw new Error(`第 ${index + 1} 行数据不完整`)
    }
    return {
      email_address: item.email_address.trim(),
      client_id: item.client_id.trim(),
      refresh_token: item.refresh_token.trim()
    }
  })
}

const parseCSVContent = (content) => {
  const lines = content.trim().split('\n')
  const accounts = []
  const startIndex = lines[0].toLowerCase().includes('email') ? 1 : 0
  
  for (let i = startIndex; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue
    
    const parts = line.split(',').map(part => part.trim().replace(/^["']|["']$/g, ''))
    if (parts.length < 3) {
      throw new Error(`第 ${i + 1} 行数据不完整`)
    }
    
    accounts.push({
      email_address: parts[0],
      client_id: parts[1],
      refresh_token: parts[2]
    })
  }
  
  return accounts
}

const parseAutoDetect = (content) => {
  try {
    return parseJSONContent(content)
  } catch (e) {
    try {
      return parseCSVContent(content)
    } catch (e2) {
      throw new Error('无法识别文件格式，请使用 JSON 或 CSV 格式')
    }
  }
}

// 手动输入
const addManualAccount = () => {
  const { email, clientId, refreshToken } = manualForm.value
  
  if (!email || !clientId || !refreshToken) {
    alert('请填写完整的账户信息')
    return
  }
  
  if (!isValidEmail(email)) {
    alert('请输入有效的邮箱地址')
    return
  }
  
  const isDuplicate = pendingAccounts.value.some(acc => acc.email_address === email)
  if (isDuplicate) {
    alert('该邮箱已在待导入列表中')
    return
  }
  
  pendingAccounts.value.push({
    email_address: email,
    client_id: clientId,
    refresh_token: refreshToken
  })
  
  manualForm.value = { email: '', clientId: '', refreshToken: '' }
  showPreview.value = true
}

const removeManualAccount = (index) => {
  pendingAccounts.value.splice(index, 1)
  if (pendingAccounts.value.length === 0) {
    showPreview.value = false
  }
}

// 剪贴板处理
const parseClipboard = () => {
  const content = clipboardText.value.trim()
  if (!content) {
    alert('请先粘贴账户数据')
    return
  }
  
  try {
    const accounts = parseAutoDetect(content)
    if (accounts.length > 0) {
      pendingAccounts.value = accounts
      showPreview.value = true
    } else {
      alert('没有找到有效的账户数据')
    }
  } catch (error) {
    console.error('剪贴板数据解析失败:', error)
    alert(`数据解析失败: ${error.message}`)
  }
}

// 开始导入
const startImport = async () => {
  if (validAccounts.value.length === 0) {
    alert('没有有效的账户可以导入')
    return
  }
  
  isImporting.value = true
  showProgress.value = true
  
  try {
    await emit('import-accounts', validAccounts.value, {
      onProgress: (current, total, currentAccount) => {
        progress.value = (current / total) * 100
        progressText.value = `导入中... (${current}/${total})`
        progressDetails.value = `正在导入: ${currentAccount}`
      }
    })
    
    progressText.value = '导入完成'
    setTimeout(() => {
      resetModal() // 重置弹窗状态
      closeModal()
    }, 1000)
  } catch (error) {
    progressText.value = '导入失败'
    progressDetails.value = error.message
  } finally {
    isImporting.value = false
  }
}

// 工具函数
const validateAccountData = (account) => {
  if (!account.email_address || !account.client_id || !account.refresh_token) {
    return { isValid: false, reason: '数据不完整' }
  }
  
  if (!isValidEmail(account.email_address)) {
    return { isValid: false, reason: '邮箱格式无效' }
  }
  
  if (account.client_id.length < 10) {
    return { isValid: false, reason: 'Client ID 过短' }
  }
  
  if (account.refresh_token.length < 20) {
    return { isValid: false, reason: 'Refresh Token 过短' }
  }
  
  return { isValid: true }
}

const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
</script>

<style scoped>
/* 组件特定样式 */
.preview-reason {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 2px;
}

.preview-info {
  flex: 1;
}

.preview-email {
  font-weight: 600;
  color: var(--text-primary);
}
</style>